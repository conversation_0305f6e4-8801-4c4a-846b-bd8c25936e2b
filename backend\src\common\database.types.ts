/* tslint:disable */
/* eslint-disable */


/**
 * AUTO-GENERATED FILE - DO NOT EDIT!
 *
 * This file was automatically generated by pg-to-ts v.4.1.1
 * $ pg-to-ts generate -c postgresql://username:<EMAIL>:5432/postgres -t agentes -t agentes_companeros -t agentes_sops_y_guias -t agentes_tareas -t agentes_tools -t chat_performance_metrics -t comunicaciones -t configuracion -t contratos -t departamentos -t diagnosticos -t doc_exports -t documentos_empresa -t empresas -t etiquetas -t evaluaciones -t facturas -t grabaciones -t hallazgos_clientes -t hallazgos_reuniones_departamentos -t human_intervention_requests -t ideas -t llm_models -t llm_providers -t logs_sistema -t mejoras_agentes -t notificaciones -t oportunidades -t personas -t plantillas_tareas -t preguntas -t procesos -t procesos_clientes -t procesos_clientes_departamentos -t procesos_clientes_responsables -t procesos_fuentes_informacion -t procesos_plantillas -t procesos_tareas_plantilla -t proyecto_personas -t proyectos -t proyectos_empresas -t proyectos_procesos -t retos_subtareas -t retos_usuarios -t reunion_empresas_asociadas -t reunion_personas_asociadas -t reunion_speaker_asignaciones -t reuniones -t sops_y_guias -t tareas -t tareas_clientes -t tareas_clientes_responsables -t tareas_empresas -t tareas_etiquetas -t thread_message_counts -t threads -t threads_metadata -t threads_resumen -t ticket_mensajes -t tickets -t tools -t usuarios -t workflow_errores -t workflows -s public
 *
 */


export type Json = unknown;
export type enum_ideas_estado = 'descartada' | 'implementada' | 'pendiente';
export type enum_ideas_prioridad = 'Alta' | 'Baja' | 'Media' | 'Urgente';
export type estado_contrato = 'activo' | 'borrador' | 'cancelado' | 'en_pausa' | 'finalizado' | 'firmado' | 'pendiente_firma' | 'renovado';
export type estado_evaluacion_enum = 'descartado' | 'pendiente' | 'revisado';
export type estado_factura = 'anulada' | 'borrador' | 'cancelada' | 'emitida' | 'enviada' | 'pagada' | 'vencida';
export type estado_hallazgo_enum = 'identificado' | 'pendiente_revision_humana';
export type estado_mejora = 'aplicado' | 'aprobado_para_aplicar' | 'descartado' | 'pendiente_revision_humana';
export type estado_mejora_agente_enum = 'aplicado' | 'aprobado' | 'descartado' | 'pendiente';
export type estado_proceso_cliente_enum = 'definicion_completa' | 'definicion_parcial' | 'identificado' | 'pendiente_revision_humana';
export type estado_tarea = 'Bloqueada' | 'Completada' | 'En Progreso' | 'En Revisión' | 'Pendiente';
export type evaluaciones_estado_enum = 'mejoras_aplicadas' | 'pendiente_mejoras' | 'pendiente_revision' | 'revisado';
export type grabacion_entidad_tipo = 'pregunta_cliente' | 'proceso_cliente' | 'tarea_cliente';
export type grabacion_estado_procesamiento = 'completado' | 'en_procesamiento' | 'error' | 'pendiente';
export type grabacion_tipo = 'solo_audio' | 'video_pantalla';
export type intervention_status = 'cancelado' | 'fallido' | 'pendiente' | 'resuelto';
export type pregunta_estado = 'en_procesamiento_ia' | 'pendiente_respuesta' | 'procesada' | 'respondida';
export type pregunta_visibilidad = 'interno_aceleralia' | 'portal_cliente';
export type prioridad_general_enum = 'Alta' | 'Baja' | 'Media' | 'Urgente';
export type prioridad_tarea = 'Alta' | 'Baja' | 'Media' | 'Urgente';
export type procesos_fuentes_informacion_estado = 'obsoleto' | 'pendiente_revision' | 'valido';
export type reto_estado = 'completado' | 'expirado' | 'pendiente';
export type reto_tipo_accion = 'definir_duracion_proceso' | 'definir_proceso' | 'definir_tarea' | 'responder_pregunta';
export type rol_decision_lead_enum = 'Influenciador' | 'Otro' | 'Tomador de decision';
export type sop_guia_type_enum = 'Guia' | 'SOP';
export type task_status = 'cancelled' | 'completed' | 'failed' | 'interrupted' | 'pending' | 'ready' | 'running';
export type thread_request_type_enum = 'API' | 'chat';
export type tipo_complejidad_automatizacion = 'alta' | 'baja' | 'media' | 'muy_alta' | 'pendiente_de_analisis';
export type tipo_frecuencia_periodo = 'anual' | 'diario' | 'indefinido' | 'mensual' | 'semanal' | 'trimestral';
export type tipo_hallazgo_enum = 'deficit_gobernanza_datos' | 'equipamiento_inadecuado' | 'falta_estandarizacion' | 'ineficiencia' | 'ladron_tiempo' | 'oportunidad_mejora' | 'riesgo_identificado';
export type tipo_prioridad_automatizacion = 'alta' | 'baja' | 'critica' | 'media' | 'pendiente_de_analisis';
export type tipo_proceso_enum = 'Externo' | 'Interno';
export type tipo_relacion_empresa_enum = 'Cliente' | 'Colaborador' | 'Lead' | 'Otro';
export type urgencia_tarea_enum = 'No Urgente' | 'Urgente';

// Table agentes
export interface Agentes {
  id: string;
  nombre: string;
  descripcion: string | null;
  system_prompt: string;
  activo: boolean | null;
  created_at: Date | null;
  updated_at: Date | null;
  selected_llm_model_id: string | null;
  db_schema: boolean | null;
  temperature: number | null;
}
export interface AgentesInput {
  id?: string;
  nombre: string;
  descripcion?: string | null;
  system_prompt: string;
  activo?: boolean | null;
  created_at?: Date | null;
  updated_at?: Date | null;
  selected_llm_model_id?: string | null;
  db_schema?: boolean | null;
  temperature?: number | null;
}
const agentes = {
  tableName: 'agentes',
  columns: ['id', 'nombre', 'descripcion', 'system_prompt', 'activo', 'created_at', 'updated_at', 'selected_llm_model_id', 'db_schema', 'temperature'],
  requiredForInsert: ['nombre', 'system_prompt'],
  primaryKey: 'id',
  foreignKeys: { selected_llm_model_id: { table: 'llm_models', column: 'id', $type: null as unknown as LlmModels }, },
  $type: null as unknown as Agentes,
  $input: null as unknown as AgentesInput
} as const;

// Table agentes_companeros
export interface AgentesCompaneros {
  agente_id: string;
  companero_id: string;
  created_at: Date | null;
  id: string;
}
export interface AgentesCompanerosInput {
  agente_id: string;
  companero_id: string;
  created_at?: Date | null;
  id?: string;
}
const agentes_companeros = {
  tableName: 'agentes_companeros',
  columns: ['agente_id', 'companero_id', 'created_at', 'id'],
  requiredForInsert: ['agente_id', 'companero_id'],
  primaryKey: 'id',
  foreignKeys: {
    agente_id: { table: 'agentes', column: 'id', $type: null as unknown as Agentes },
    companero_id: { table: 'agentes', column: 'id', $type: null as unknown as Agentes },
  },
  $type: null as unknown as AgentesCompaneros,
  $input: null as unknown as AgentesCompanerosInput
} as const;

// Table agentes_sops_y_guias
export interface AgentesSopsYGuias {
  id: string;
  agente_id: string;
  sop_guia_id: string;
  created_at: Date | null;
}
export interface AgentesSopsYGuiasInput {
  id?: string;
  agente_id: string;
  sop_guia_id: string;
  created_at?: Date | null;
}
const agentes_sops_y_guias = {
  tableName: 'agentes_sops_y_guias',
  columns: ['id', 'agente_id', 'sop_guia_id', 'created_at'],
  requiredForInsert: ['agente_id', 'sop_guia_id'],
  primaryKey: 'id',
  foreignKeys: {
    agente_id: { table: 'agentes', column: 'id', $type: null as unknown as Agentes },
    sop_guia_id: { table: 'sops_y_guias', column: 'id', $type: null as unknown as SopsYGuias },
  },
  $type: null as unknown as AgentesSopsYGuias,
  $input: null as unknown as AgentesSopsYGuiasInput
} as const;

// Table agentes_tareas
/** Registro de tareas asignadas a los asistentes de IA, permitiendo trazabilidad y gestión. */
export interface AgentesTareas {
  id: string;
  agente_id: string;
  descripcion: string;
  input_data: Json | null;
  /** Estado actual de la tarea: pendiente, en_progreso, completada, fallida, esperando_hitl. */
  estado: task_status;
  resultado: Json | null;
  prioridad: number | null;
  tarea_origen_id: string | null;
  tarea_origen_tipo: string | null;
  /** Indica si la tarea fue creada por un humano o por otro agente. */
  creado_por_tipo: string;
  creado_por_id: string;
  iniciado_en: Date | null;
  finalizado_en: Date | null;
  created_at: Date;
  updated_at: Date;
  langgraph_thread_id: string | null;
  titulo: string;
  error_mensaje: string | null;
  error_detalles: Json | null;
}
/** Registro de tareas asignadas a los asistentes de IA, permitiendo trazabilidad y gestión. */
export interface AgentesTareasInput {
  id?: string;
  agente_id: string;
  descripcion: string;
  input_data?: Json | null;
  /** Estado actual de la tarea: pendiente, en_progreso, completada, fallida, esperando_hitl. */
  estado?: task_status;
  resultado?: Json | null;
  prioridad?: number | null;
  tarea_origen_id?: string | null;
  tarea_origen_tipo?: string | null;
  /** Indica si la tarea fue creada por un humano o por otro agente. */
  creado_por_tipo: string;
  creado_por_id: string;
  iniciado_en?: Date | null;
  finalizado_en?: Date | null;
  created_at?: Date;
  updated_at?: Date;
  langgraph_thread_id?: string | null;
  titulo: string;
  error_mensaje?: string | null;
  error_detalles?: Json | null;
}
const agentes_tareas = {
  tableName: 'agentes_tareas',
  columns: ['id', 'agente_id', 'descripcion', 'input_data', 'estado', 'resultado', 'prioridad', 'tarea_origen_id', 'tarea_origen_tipo', 'creado_por_tipo', 'creado_por_id', 'iniciado_en', 'finalizado_en', 'created_at', 'updated_at', 'langgraph_thread_id', 'titulo', 'error_mensaje', 'error_detalles'],
  requiredForInsert: ['agente_id', 'descripcion', 'creado_por_tipo', 'creado_por_id', 'titulo'],
  primaryKey: 'id',
  foreignKeys: { agente_id: { table: 'agentes', column: 'id', $type: null as unknown as Agentes }, },
  $type: null as unknown as AgentesTareas,
  $input: null as unknown as AgentesTareasInput
} as const;

// Table agentes_tools
export interface AgentesTools {
  agente_id: string;
  tool_id: string;
  created_at: Date | null;
  id: string;
}
export interface AgentesToolsInput {
  agente_id: string;
  tool_id: string;
  created_at?: Date | null;
  id?: string;
}
const agentes_tools = {
  tableName: 'agentes_tools',
  columns: ['agente_id', 'tool_id', 'created_at', 'id'],
  requiredForInsert: ['agente_id', 'tool_id'],
  primaryKey: 'id',
  foreignKeys: {
    agente_id: { table: 'agentes', column: 'id', $type: null as unknown as Agentes },
    tool_id: { table: 'tools', column: 'id', $type: null as unknown as Tools },
  },
  $type: null as unknown as AgentesTools,
  $input: null as unknown as AgentesToolsInput
} as const;

// Table chat_performance_metrics
export interface ChatPerformanceMetrics {
  thread_id: number | null;
  total_messages: number | null;
  user_messages: number | null;
  agent_answers: number | null;
  intermediate_steps: number | null;
  last_activity: Date | null;
  first_activity: Date | null;
  conversation_duration_hours: number | null;
  avg_message_length: number | null;
  total_content_length: number | null;
}
export interface ChatPerformanceMetricsInput {
  thread_id?: number | null;
  total_messages?: number | null;
  user_messages?: number | null;
  agent_answers?: number | null;
  intermediate_steps?: number | null;
  last_activity?: Date | null;
  first_activity?: Date | null;
  conversation_duration_hours?: number | null;
  avg_message_length?: number | null;
  total_content_length?: number | null;
}
const chat_performance_metrics = {
  tableName: 'chat_performance_metrics',
  columns: ['thread_id', 'total_messages', 'user_messages', 'agent_answers', 'intermediate_steps', 'last_activity', 'first_activity', 'conversation_duration_hours', 'avg_message_length', 'total_content_length'],
  requiredForInsert: [],
  primaryKey: null,
  foreignKeys: {},
  $type: null as unknown as ChatPerformanceMetrics,
  $input: null as unknown as ChatPerformanceMetricsInput
} as const;

// Table comunicaciones
export interface Comunicaciones {
  id: string;
  tipo: string;
  fecha_hora: Date | null;
  direccion: string | null;
  asunto: string | null;
  contenido: string | null;
  transcripcion_url: string | null;
  duracion_minutos: number | null;
  participante_usuario_id: string | null;
  participante_persona_id: string | null;
  participante_externo_detalle: string | null;
  relacionado_oportunidad_id: string | null;
  relacionado_empresa_id: string | null;
  relacionado_proyecto_id: string | null;
  relacionado_tarea_id: string | null;
  fuente_sistema: string | null;
  created_at: Date | null;
}
export interface ComunicacionesInput {
  id?: string;
  tipo: string;
  fecha_hora?: Date | null;
  direccion?: string | null;
  asunto?: string | null;
  contenido?: string | null;
  transcripcion_url?: string | null;
  duracion_minutos?: number | null;
  participante_usuario_id?: string | null;
  participante_persona_id?: string | null;
  participante_externo_detalle?: string | null;
  relacionado_oportunidad_id?: string | null;
  relacionado_empresa_id?: string | null;
  relacionado_proyecto_id?: string | null;
  relacionado_tarea_id?: string | null;
  fuente_sistema?: string | null;
  created_at?: Date | null;
}
const comunicaciones = {
  tableName: 'comunicaciones',
  columns: ['id', 'tipo', 'fecha_hora', 'direccion', 'asunto', 'contenido', 'transcripcion_url', 'duracion_minutos', 'participante_usuario_id', 'participante_persona_id', 'participante_externo_detalle', 'relacionado_oportunidad_id', 'relacionado_empresa_id', 'relacionado_proyecto_id', 'relacionado_tarea_id', 'fuente_sistema', 'created_at'],
  requiredForInsert: ['tipo'],
  primaryKey: 'id',
  foreignKeys: {
    participante_usuario_id: { table: 'usuarios', column: 'id', $type: null as unknown as Usuarios },
    participante_persona_id: { table: 'personas', column: 'id', $type: null as unknown as Personas },
    relacionado_oportunidad_id: { table: 'oportunidades', column: 'id', $type: null as unknown as Oportunidades },
    relacionado_empresa_id: { table: 'empresas', column: 'id', $type: null as unknown as Empresas },
    relacionado_proyecto_id: { table: 'proyectos', column: 'id', $type: null as unknown as Proyectos },
    relacionado_tarea_id: { table: 'tareas', column: 'id', $type: null as unknown as Tareas },
  },
  $type: null as unknown as Comunicaciones,
  $input: null as unknown as ComunicacionesInput
} as const;

// Table configuracion
export interface Configuracion {
  id: string;
  clave: string;
  valor: string;
  descripcion: string | null;
  created_at: Date;
  updated_at: Date;
}
export interface ConfiguracionInput {
  id?: string;
  clave: string;
  valor: string;
  descripcion?: string | null;
  created_at?: Date;
  updated_at?: Date;
}
const configuracion = {
  tableName: 'configuracion',
  columns: ['id', 'clave', 'valor', 'descripcion', 'created_at', 'updated_at'],
  requiredForInsert: ['clave', 'valor'],
  primaryKey: 'id',
  foreignKeys: {},
  $type: null as unknown as Configuracion,
  $input: null as unknown as ConfiguracionInput
} as const;

// Table contratos
export interface Contratos {
  id: string;
  codigo: string;
  titulo: string;
  empresa_id: string;
  proyecto_id: string | null;
  fecha_inicio: Date;
  fecha_fin: Date | null;
  renovacion_automatica: boolean | null;
  periodo_renovacion: string | null;
  valor_contrato: number | null;
  moneda: string | null;
  periodicidad_facturacion: string | null;
  dia_facturacion: number | null;
  estado: estado_contrato;
  fecha_firma: Date | null;
  firmado_cliente: boolean | null;
  firmado_empresa: boolean | null;
  url_documento: string | null;
  persona_firma_id: string | null;
  terminos_condiciones: string | null;
  clausulas: Json | null;
  fecha_cancelacion: Date | null;
  motivo_cancelacion: string | null;
  contrato_relacionado_id: string | null;
  info_adicional: string | null;
  creado_por: string | null;
  created_at: Date | null;
  updated_at: Date | null;
}
export interface ContratosInput {
  id?: string;
  codigo: string;
  titulo: string;
  empresa_id: string;
  proyecto_id?: string | null;
  fecha_inicio: Date;
  fecha_fin?: Date | null;
  renovacion_automatica?: boolean | null;
  periodo_renovacion?: string | null;
  valor_contrato?: number | null;
  moneda?: string | null;
  periodicidad_facturacion?: string | null;
  dia_facturacion?: number | null;
  estado?: estado_contrato;
  fecha_firma?: Date | null;
  firmado_cliente?: boolean | null;
  firmado_empresa?: boolean | null;
  url_documento?: string | null;
  persona_firma_id?: string | null;
  terminos_condiciones?: string | null;
  clausulas?: Json | null;
  fecha_cancelacion?: Date | null;
  motivo_cancelacion?: string | null;
  contrato_relacionado_id?: string | null;
  info_adicional?: string | null;
  creado_por?: string | null;
  created_at?: Date | null;
  updated_at?: Date | null;
}
const contratos = {
  tableName: 'contratos',
  columns: ['id', 'codigo', 'titulo', 'empresa_id', 'proyecto_id', 'fecha_inicio', 'fecha_fin', 'renovacion_automatica', 'periodo_renovacion', 'valor_contrato', 'moneda', 'periodicidad_facturacion', 'dia_facturacion', 'estado', 'fecha_firma', 'firmado_cliente', 'firmado_empresa', 'url_documento', 'persona_firma_id', 'terminos_condiciones', 'clausulas', 'fecha_cancelacion', 'motivo_cancelacion', 'contrato_relacionado_id', 'info_adicional', 'creado_por', 'created_at', 'updated_at'],
  requiredForInsert: ['codigo', 'titulo', 'empresa_id', 'fecha_inicio'],
  primaryKey: 'id',
  foreignKeys: {
    empresa_id: { table: 'empresas', column: 'id', $type: null as unknown as Empresas },
    proyecto_id: { table: 'proyectos', column: 'id', $type: null as unknown as Proyectos },
    contrato_relacionado_id: { table: 'contratos', column: 'id', $type: null as unknown as Contratos },
  },
  $type: null as unknown as Contratos,
  $input: null as unknown as ContratosInput
} as const;

// Table departamentos
export interface Departamentos {
  id: string;
  empresa_id: string;
  nombre: string;
  descripcion: string | null;
  created_at: Date | null;
  updated_at: Date | null;
  info_adicional: string | null;
}
export interface DepartamentosInput {
  id?: string;
  empresa_id: string;
  nombre: string;
  descripcion?: string | null;
  created_at?: Date | null;
  updated_at?: Date | null;
  info_adicional?: string | null;
}
const departamentos = {
  tableName: 'departamentos',
  columns: ['id', 'empresa_id', 'nombre', 'descripcion', 'created_at', 'updated_at', 'info_adicional'],
  requiredForInsert: ['empresa_id', 'nombre'],
  primaryKey: 'id',
  foreignKeys: { empresa_id: { table: 'empresas', column: 'id', $type: null as unknown as Empresas }, },
  $type: null as unknown as Departamentos,
  $input: null as unknown as DepartamentosInput
} as const;

// Table diagnosticos
export interface Diagnosticos {
  id: string;
  empresa_id: string | null;
  titulo: string | null;
  fecha_generacion: Date | null;
  diagrama_reactflow: string | null;
  plan_aceleracion_html: string | null;
  estado: string | null;
  version: number | null;
  creado_por: string | null;
  created_at: Date | null;
  updated_at: Date | null;
  diagrama_proceso_json: Json | null;
}
export interface DiagnosticosInput {
  id?: string;
  empresa_id?: string | null;
  titulo?: string | null;
  fecha_generacion?: Date | null;
  diagrama_reactflow?: string | null;
  plan_aceleracion_html?: string | null;
  estado?: string | null;
  version?: number | null;
  creado_por?: string | null;
  created_at?: Date | null;
  updated_at?: Date | null;
  diagrama_proceso_json?: Json | null;
}
const diagnosticos = {
  tableName: 'diagnosticos',
  columns: ['id', 'empresa_id', 'titulo', 'fecha_generacion', 'diagrama_reactflow', 'plan_aceleracion_html', 'estado', 'version', 'creado_por', 'created_at', 'updated_at', 'diagrama_proceso_json'],
  requiredForInsert: [],
  primaryKey: 'id',
  foreignKeys: {
    empresa_id: { table: 'empresas', column: 'id', $type: null as unknown as Empresas },
    creado_por: { table: 'usuarios', column: 'id', $type: null as unknown as Usuarios },
  },
  $type: null as unknown as Diagnosticos,
  $input: null as unknown as DiagnosticosInput
} as const;

// Table doc_exports
export interface DocExports {
  id: string;
  threads_row_id: string;
  user_id: string;
  status: string;
  doc_url: string | null;
  created_at: Date;
  updated_at: Date;
}
export interface DocExportsInput {
  id?: string;
  threads_row_id: string;
  user_id: string;
  status: string;
  doc_url?: string | null;
  created_at?: Date;
  updated_at?: Date;
}
const doc_exports = {
  tableName: 'doc_exports',
  columns: ['id', 'threads_row_id', 'user_id', 'status', 'doc_url', 'created_at', 'updated_at'],
  requiredForInsert: ['threads_row_id', 'user_id', 'status'],
  primaryKey: 'id',
  foreignKeys: {
    threads_row_id: { table: 'threads', column: 'id', $type: null as unknown as Threads },
    user_id: { table: 'users', column: 'id', $type: null as unknown /* users */ },
  },
  $type: null as unknown as DocExports,
  $input: null as unknown as DocExportsInput
} as const;

// Table documentos_empresa
export interface DocumentosEmpresa {
  id: string;
  nombre_documento: string | null;
  url_documento: string | null;
  ruta_carpeta_drive: string | null;
  resumen: string | null;
  usuario_documento_id: string | null;
  creado_por_agente_id: string | null;
  empresa_relacionada_id: string | null;
  proyecto_relacionado_id: string | null;
  proceso_relacionado_id: string | null;
  info_adicional: string | null;
  created_at: Date | null;
  updated_at: Date | null;
}
export interface DocumentosEmpresaInput {
  id?: string;
  nombre_documento?: string | null;
  url_documento?: string | null;
  ruta_carpeta_drive?: string | null;
  resumen?: string | null;
  usuario_documento_id?: string | null;
  creado_por_agente_id?: string | null;
  empresa_relacionada_id?: string | null;
  proyecto_relacionado_id?: string | null;
  proceso_relacionado_id?: string | null;
  info_adicional?: string | null;
  created_at?: Date | null;
  updated_at?: Date | null;
}
const documentos_empresa = {
  tableName: 'documentos_empresa',
  columns: ['id', 'nombre_documento', 'url_documento', 'ruta_carpeta_drive', 'resumen', 'usuario_documento_id', 'creado_por_agente_id', 'empresa_relacionada_id', 'proyecto_relacionado_id', 'proceso_relacionado_id', 'info_adicional', 'created_at', 'updated_at'],
  requiredForInsert: [],
  primaryKey: 'id',
  foreignKeys: {
    usuario_documento_id: { table: 'usuarios', column: 'id', $type: null as unknown as Usuarios },
    creado_por_agente_id: { table: 'agentes', column: 'id', $type: null as unknown as Agentes },
    empresa_relacionada_id: { table: 'empresas', column: 'id', $type: null as unknown as Empresas },
    proyecto_relacionado_id: { table: 'proyectos', column: 'id', $type: null as unknown as Proyectos },
    proceso_relacionado_id: { table: 'procesos', column: 'id', $type: null as unknown as Procesos },
  },
  $type: null as unknown as DocumentosEmpresa,
  $input: null as unknown as DocumentosEmpresaInput
} as const;

// Table empresas
export interface Empresas {
  id: string;
  nombre: string;
  sector: string | null;
  logo_url: string | null;
  direccion: string | null;
  telefono: string | null;
  email_principal: string | null;
  website: string | null;
  fecha_alta: Date | null;
  activo: boolean | null;
  created_at: Date | null;
  updated_at: Date | null;
  info_adicional: string | null;
  nif_cif: string | null;
  direccion_fiscal: string | null;
  tipo_empresa: string | null;
  descripcion: string | null;
  tipo_relacion: tipo_relacion_empresa_enum | null;
}
export interface EmpresasInput {
  id?: string;
  nombre: string;
  sector?: string | null;
  logo_url?: string | null;
  direccion?: string | null;
  telefono?: string | null;
  email_principal?: string | null;
  website?: string | null;
  fecha_alta?: Date | null;
  activo?: boolean | null;
  created_at?: Date | null;
  updated_at?: Date | null;
  info_adicional?: string | null;
  nif_cif?: string | null;
  direccion_fiscal?: string | null;
  tipo_empresa?: string | null;
  descripcion?: string | null;
  tipo_relacion?: tipo_relacion_empresa_enum | null;
}
const empresas = {
  tableName: 'empresas',
  columns: ['id', 'nombre', 'sector', 'logo_url', 'direccion', 'telefono', 'email_principal', 'website', 'fecha_alta', 'activo', 'created_at', 'updated_at', 'info_adicional', 'nif_cif', 'direccion_fiscal', 'tipo_empresa', 'descripcion', 'tipo_relacion'],
  requiredForInsert: ['nombre'],
  primaryKey: 'id',
  foreignKeys: {},
  $type: null as unknown as Empresas,
  $input: null as unknown as EmpresasInput
} as const;

// Table etiquetas
export interface Etiquetas {
  id: string;
  nombre: string;
  color: string | null;
  descripcion: string | null;
  created_at: Date | null;
  updated_at: Date | null;
}
export interface EtiquetasInput {
  id?: string;
  nombre: string;
  color?: string | null;
  descripcion?: string | null;
  created_at?: Date | null;
  updated_at?: Date | null;
}
const etiquetas = {
  tableName: 'etiquetas',
  columns: ['id', 'nombre', 'color', 'descripcion', 'created_at', 'updated_at'],
  requiredForInsert: ['nombre'],
  primaryKey: 'id',
  foreignKeys: {},
  $type: null as unknown as Etiquetas,
  $input: null as unknown as EtiquetasInput
} as const;

// Table evaluaciones
export interface Evaluaciones {
  id: number;
  created_at: Date;
  nombre_agente_workflow: string | null;
  proposito_agente: string | null;
  criterios_adicionales_evaluacion: string | null;
  agente_output: string | null;
  puntuacion: number | null;
  argumentos_puntuacion: string | null;
  sugerencias_mejora: string | null;
  execution_id: string | null;
  n8n_workflow_id: string | null;
  estado: evaluaciones_estado_enum | null;
  agente_id: string | null;
  nombre_agente_amigable: string | null;
  mejora_agente_id: string | null;
  user_id: string | null;
}
export interface EvaluacionesInput {
  id: number;
  created_at?: Date;
  nombre_agente_workflow?: string | null;
  proposito_agente?: string | null;
  criterios_adicionales_evaluacion?: string | null;
  agente_output?: string | null;
  puntuacion?: number | null;
  argumentos_puntuacion?: string | null;
  sugerencias_mejora?: string | null;
  execution_id?: string | null;
  n8n_workflow_id?: string | null;
  estado?: evaluaciones_estado_enum | null;
  agente_id?: string | null;
  nombre_agente_amigable?: string | null;
  mejora_agente_id?: string | null;
  user_id?: string | null;
}
const evaluaciones = {
  tableName: 'evaluaciones',
  columns: ['id', 'created_at', 'nombre_agente_workflow', 'proposito_agente', 'criterios_adicionales_evaluacion', 'agente_output', 'puntuacion', 'argumentos_puntuacion', 'sugerencias_mejora', 'execution_id', 'n8n_workflow_id', 'estado', 'agente_id', 'nombre_agente_amigable', 'mejora_agente_id', 'user_id'],
  requiredForInsert: ['id'],
  primaryKey: 'id',
  foreignKeys: {
    mejora_agente_id: { table: 'mejoras_agentes', column: 'id', $type: null as unknown as MejorasAgentes },
    user_id: { table: 'usuarios', column: 'auth_user_id', $type: null as unknown as Usuarios },
  },
  $type: null as unknown as Evaluaciones,
  $input: null as unknown as EvaluacionesInput
} as const;

// Table facturas
export interface Facturas {
  id: string;
  numero_factura: string;
  serie: string | null;
  empresa_id: string;
  proyecto_id: string | null;
  contrato_id: string | null;
  fecha_emision: Date;
  fecha_vencimiento: Date;
  subtotal: number;
  iva: number;
  total: number;
  estado: estado_factura;
  fecha_pago: Date | null;
  metodo_pago: string | null;
  referencia_pago: string | null;
  pdf_url: string | null;
  xml_url: string | null;
  plantilla_id: string | null;
  emisor_nombre: string;
  emisor_nif: string;
  emisor_direccion: string;
  receptor_nombre: string;
  receptor_nif: string;
  receptor_direccion: string;
  items: Json;
  recordatorios_enviados: number | null;
  fecha_ultimo_recordatorio: Date | null;
  info_adicional: string | null;
  creado_por: string | null;
  created_at: Date | null;
  updated_at: Date | null;
}
export interface FacturasInput {
  id?: string;
  numero_factura: string;
  serie?: string | null;
  empresa_id: string;
  proyecto_id?: string | null;
  contrato_id?: string | null;
  fecha_emision?: Date;
  fecha_vencimiento: Date;
  subtotal: number;
  iva: number;
  total: number;
  estado?: estado_factura;
  fecha_pago?: Date | null;
  metodo_pago?: string | null;
  referencia_pago?: string | null;
  pdf_url?: string | null;
  xml_url?: string | null;
  plantilla_id?: string | null;
  emisor_nombre: string;
  emisor_nif: string;
  emisor_direccion: string;
  receptor_nombre: string;
  receptor_nif: string;
  receptor_direccion: string;
  items?: Json;
  recordatorios_enviados?: number | null;
  fecha_ultimo_recordatorio?: Date | null;
  info_adicional?: string | null;
  creado_por?: string | null;
  created_at?: Date | null;
  updated_at?: Date | null;
}
const facturas = {
  tableName: 'facturas',
  columns: ['id', 'numero_factura', 'serie', 'empresa_id', 'proyecto_id', 'contrato_id', 'fecha_emision', 'fecha_vencimiento', 'subtotal', 'iva', 'total', 'estado', 'fecha_pago', 'metodo_pago', 'referencia_pago', 'pdf_url', 'xml_url', 'plantilla_id', 'emisor_nombre', 'emisor_nif', 'emisor_direccion', 'receptor_nombre', 'receptor_nif', 'receptor_direccion', 'items', 'recordatorios_enviados', 'fecha_ultimo_recordatorio', 'info_adicional', 'creado_por', 'created_at', 'updated_at'],
  requiredForInsert: ['numero_factura', 'empresa_id', 'fecha_vencimiento', 'subtotal', 'iva', 'total', 'emisor_nombre', 'emisor_nif', 'emisor_direccion', 'receptor_nombre', 'receptor_nif', 'receptor_direccion'],
  primaryKey: 'id',
  foreignKeys: {
    empresa_id: { table: 'empresas', column: 'id', $type: null as unknown as Empresas },
    proyecto_id: { table: 'proyectos', column: 'id', $type: null as unknown as Proyectos },
    contrato_id: { table: 'contratos', column: 'id', $type: null as unknown as Contratos },
  },
  $type: null as unknown as Facturas,
  $input: null as unknown as FacturasInput
} as const;

// Table grabaciones
/** Almacena metadatos de todas las grabaciones subidas, asociadas a diferentes entidades. */
export interface Grabaciones {
  id: string;
  entidad_relacionada_id: string;
  entidad_relacionada_tipo: grabacion_entidad_tipo;
  usuario_id: string;
  url_almacenamiento: string;
  estado_procesamiento: grabacion_estado_procesamiento;
  tipo_grabacion: grabacion_tipo;
  transcripcion: string | null;
  duracion_segundos: number | null;
  info_adicional: string | null;
  created_at: Date;
  updated_at: Date;
}
/** Almacena metadatos de todas las grabaciones subidas, asociadas a diferentes entidades. */
export interface GrabacionesInput {
  id?: string;
  entidad_relacionada_id: string;
  entidad_relacionada_tipo: grabacion_entidad_tipo;
  usuario_id: string;
  url_almacenamiento: string;
  estado_procesamiento?: grabacion_estado_procesamiento;
  tipo_grabacion: grabacion_tipo;
  transcripcion?: string | null;
  duracion_segundos?: number | null;
  info_adicional?: string | null;
  created_at?: Date;
  updated_at?: Date;
}
const grabaciones = {
  tableName: 'grabaciones',
  columns: ['id', 'entidad_relacionada_id', 'entidad_relacionada_tipo', 'usuario_id', 'url_almacenamiento', 'estado_procesamiento', 'tipo_grabacion', 'transcripcion', 'duracion_segundos', 'info_adicional', 'created_at', 'updated_at'],
  requiredForInsert: ['entidad_relacionada_id', 'entidad_relacionada_tipo', 'usuario_id', 'url_almacenamiento', 'tipo_grabacion'],
  primaryKey: 'id',
  foreignKeys: { usuario_id: { table: 'usuarios', column: 'id', $type: null as unknown as Usuarios }, },
  $type: null as unknown as Grabaciones,
  $input: null as unknown as GrabacionesInput
} as const;

// Table hallazgos_clientes
export interface HallazgosClientes {
  id: string;
  reunion_id: string;
  empresa_id: string;
  persona_id: string | null;
  /** Tipo de hallazgo identificado durante la reunión. Valores posibles: ineficiencia, ladron_tiempo, oportunidad_mejora, riesgo_identificado, deficit_gobernanza_datos, falta_estandarizacion, equipamiento_inadecuado. */
  tipo: tipo_hallazgo_enum | null;
  descripcion: string | null;
  impacto: string | null;
  posible_solucion: string | null;
  estado: estado_hallazgo_enum | null;
  created_at: Date | null;
  updated_at: Date | null;
  /** Array de nombres de procesos relacionados con este hallazgo */
  procesos_relacionados: Json | null;
  titulo: string;
}
export interface HallazgosClientesInput {
  id?: string;
  reunion_id: string;
  empresa_id: string;
  persona_id?: string | null;
  /** Tipo de hallazgo identificado durante la reunión. Valores posibles: ineficiencia, ladron_tiempo, oportunidad_mejora, riesgo_identificado, deficit_gobernanza_datos, falta_estandarizacion, equipamiento_inadecuado. */
  tipo?: tipo_hallazgo_enum | null;
  descripcion?: string | null;
  impacto?: string | null;
  posible_solucion?: string | null;
  estado?: estado_hallazgo_enum | null;
  created_at?: Date | null;
  updated_at?: Date | null;
  /** Array de nombres de procesos relacionados con este hallazgo */
  procesos_relacionados?: Json | null;
  titulo: string;
}
const hallazgos_clientes = {
  tableName: 'hallazgos_clientes',
  columns: ['id', 'reunion_id', 'empresa_id', 'persona_id', 'tipo', 'descripcion', 'impacto', 'posible_solucion', 'estado', 'created_at', 'updated_at', 'procesos_relacionados', 'titulo'],
  requiredForInsert: ['reunion_id', 'empresa_id', 'titulo'],
  primaryKey: 'id',
  foreignKeys: {
    reunion_id: { table: 'reuniones', column: 'id', $type: null as unknown as Reuniones },
    empresa_id: { table: 'empresas', column: 'id', $type: null as unknown as Empresas },
    persona_id: { table: 'personas', column: 'id', $type: null as unknown as Personas },
  },
  $type: null as unknown as HallazgosClientes,
  $input: null as unknown as HallazgosClientesInput
} as const;

// Table hallazgos_reuniones_departamentos
export interface HallazgosReunionesDepartamentos {
  hallazgo_reunion_id: string;
  departamento_id: string;
  created_at: Date;
}
export interface HallazgosReunionesDepartamentosInput {
  hallazgo_reunion_id: string;
  departamento_id: string;
  created_at?: Date;
}
const hallazgos_reuniones_departamentos = {
  tableName: 'hallazgos_reuniones_departamentos',
  columns: ['hallazgo_reunion_id', 'departamento_id', 'created_at'],
  requiredForInsert: ['hallazgo_reunion_id', 'departamento_id'],
  primaryKey: 'hallazgo_reunion_id',
  foreignKeys: {
    hallazgo_reunion_id: { table: 'hallazgos_clientes', column: 'id', $type: null as unknown as HallazgosClientes },
    departamento_id: { table: 'departamentos', column: 'id', $type: null as unknown as Departamentos },
  },
  $type: null as unknown as HallazgosReunionesDepartamentos,
  $input: null as unknown as HallazgosReunionesDepartamentosInput
} as const;

// Table human_intervention_requests
/** Almacena las solicitudes de intervención humana generadas por los grafos LangGraph. */
export interface HumanInterventionRequests {
  id: string;
  /** Identificador único del estado del grafo LangGraph persistido (depende del MemorySaver usado). */
  langgraph_thread_id: string;
  asistente_id: string;
  /** Tipo de intervención requerida: APPROVAL, INFO, TASK. */
  request_type: string;
  request_details: Json | null;
  /** Estado de la solicitud de intervención: pendiente, resuelta, cancelada. */
  status: string;
  resolution_data: Json | null;
  requested_at: Date;
  resolved_at: Date | null;
  resolved_by: string | null;
  created_at: Date;
  updated_at: Date;
  /** The tool_call_id from the AIMessage that triggered this intervention request. */
  triggering_tool_call_id: string | null;
}
/** Almacena las solicitudes de intervención humana generadas por los grafos LangGraph. */
export interface HumanInterventionRequestsInput {
  id?: string;
  /** Identificador único del estado del grafo LangGraph persistido (depende del MemorySaver usado). */
  langgraph_thread_id: string;
  asistente_id: string;
  /** Tipo de intervención requerida: APPROVAL, INFO, TASK. */
  request_type: string;
  request_details?: Json | null;
  /** Estado de la solicitud de intervención: pendiente, resuelta, cancelada. */
  status?: string;
  resolution_data?: Json | null;
  requested_at?: Date;
  resolved_at?: Date | null;
  resolved_by?: string | null;
  created_at?: Date;
  updated_at?: Date;
  /** The tool_call_id from the AIMessage that triggered this intervention request. */
  triggering_tool_call_id?: string | null;
}
const human_intervention_requests = {
  tableName: 'human_intervention_requests',
  columns: ['id', 'langgraph_thread_id', 'asistente_id', 'request_type', 'request_details', 'status', 'resolution_data', 'requested_at', 'resolved_at', 'resolved_by', 'created_at', 'updated_at', 'triggering_tool_call_id'],
  requiredForInsert: ['langgraph_thread_id', 'asistente_id', 'request_type'],
  primaryKey: 'id',
  foreignKeys: {
    asistente_id: { table: 'agentes', column: 'id', $type: null as unknown as Agentes },
    resolved_by: { table: 'usuarios', column: 'id', $type: null as unknown as Usuarios },
  },
  $type: null as unknown as HumanInterventionRequests,
  $input: null as unknown as HumanInterventionRequestsInput
} as const;

// Table ideas
export interface Ideas {
  id: string;
  /** Título conciso de la idea. */
  titulo: string;
  /** Descripción detallada de la idea, si es necesaria. */
  descripcion: string | null;
  empresa_relacionada_id: string | null;
  proyecto_relacionado_id: string | null;
  created_at: Date | null;
  updated_at: Date | null;
  estado: enum_ideas_estado | null;
  prioridad: enum_ideas_prioridad | null;
}
export interface IdeasInput {
  id?: string;
  /** Título conciso de la idea. */
  titulo: string;
  /** Descripción detallada de la idea, si es necesaria. */
  descripcion?: string | null;
  empresa_relacionada_id?: string | null;
  proyecto_relacionado_id?: string | null;
  created_at?: Date | null;
  updated_at?: Date | null;
  estado?: enum_ideas_estado | null;
  prioridad?: enum_ideas_prioridad | null;
}
const ideas = {
  tableName: 'ideas',
  columns: ['id', 'titulo', 'descripcion', 'empresa_relacionada_id', 'proyecto_relacionado_id', 'created_at', 'updated_at', 'estado', 'prioridad'],
  requiredForInsert: ['titulo'],
  primaryKey: 'id',
  foreignKeys: {
    empresa_relacionada_id: { table: 'empresas', column: 'id', $type: null as unknown as Empresas },
    proyecto_relacionado_id: { table: 'proyectos', column: 'id', $type: null as unknown as Proyectos },
  },
  $type: null as unknown as Ideas,
  $input: null as unknown as IdeasInput
} as const;

// Table llm_models
export interface LlmModels {
  id: string;
  provider_id: string;
  model_name: string;
  description: string | null;
  context_window: number | null;
  active: boolean;
  created_at: Date;
  input_cost_per_million_tokens: number | null;
  output_cost_per_million_tokens: number | null;
}
export interface LlmModelsInput {
  id?: string;
  provider_id: string;
  model_name: string;
  description?: string | null;
  context_window?: number | null;
  active?: boolean;
  created_at?: Date;
  input_cost_per_million_tokens?: number | null;
  output_cost_per_million_tokens?: number | null;
}
const llm_models = {
  tableName: 'llm_models',
  columns: ['id', 'provider_id', 'model_name', 'description', 'context_window', 'active', 'created_at', 'input_cost_per_million_tokens', 'output_cost_per_million_tokens'],
  requiredForInsert: ['provider_id', 'model_name'],
  primaryKey: 'id',
  foreignKeys: { provider_id: { table: 'llm_providers', column: 'id', $type: null as unknown as LlmProviders }, },
  $type: null as unknown as LlmModels,
  $input: null as unknown as LlmModelsInput
} as const;

// Table llm_providers
export interface LlmProviders {
  id: string;
  provider_key: string;
  provider_name: string;
  created_at: Date;
}
export interface LlmProvidersInput {
  id?: string;
  provider_key: string;
  provider_name: string;
  created_at?: Date;
}
const llm_providers = {
  tableName: 'llm_providers',
  columns: ['id', 'provider_key', 'provider_name', 'created_at'],
  requiredForInsert: ['provider_key', 'provider_name'],
  primaryKey: 'id',
  foreignKeys: {},
  $type: null as unknown as LlmProviders,
  $input: null as unknown as LlmProvidersInput
} as const;

// Table logs_sistema
export interface LogsSistema {
  id: string;
  tipo: string;
  origen: string;
  mensaje: string;
  detalles: Json | null;
  usuario_id: string | null;
  timestamp: Date | null;
}
export interface LogsSistemaInput {
  id?: string;
  tipo: string;
  origen: string;
  mensaje: string;
  detalles?: Json | null;
  usuario_id?: string | null;
  timestamp?: Date | null;
}
const logs_sistema = {
  tableName: 'logs_sistema',
  columns: ['id', 'tipo', 'origen', 'mensaje', 'detalles', 'usuario_id', 'timestamp'],
  requiredForInsert: ['tipo', 'origen', 'mensaje'],
  primaryKey: 'id',
  foreignKeys: { usuario_id: { table: 'usuarios', column: 'id', $type: null as unknown as Usuarios }, },
  $type: null as unknown as LogsSistema,
  $input: null as unknown as LogsSistemaInput
} as const;

// Table mejoras_agentes
/** Almacena las propuestas de mejora consolidadas para los prompts de los agentes, generadas por el sistema de autoevaluación. */
export interface MejorasAgentes {
  id: string;
  agente_id: string | null;
  n8n_workflow_id: string | null;
  explicacion_mejoras: string;
  estado: estado_mejora;
  created_at: Date;
  updated_at: Date | null;
  system_prompt_original: string | null;
  user_prompt_original: string | null;
  system_prompt_mejorado: string | null;
  user_prompt_mejorado: string | null;
  set_node_id: string | null;
  nombre_agente_amigable: string | null;
  nombre_workflow: string | null;
  user_id: string;
}
/** Almacena las propuestas de mejora consolidadas para los prompts de los agentes, generadas por el sistema de autoevaluación. */
export interface MejorasAgentesInput {
  id?: string;
  agente_id?: string | null;
  n8n_workflow_id?: string | null;
  explicacion_mejoras: string;
  estado?: estado_mejora;
  created_at?: Date;
  updated_at?: Date | null;
  system_prompt_original?: string | null;
  user_prompt_original?: string | null;
  system_prompt_mejorado?: string | null;
  user_prompt_mejorado?: string | null;
  set_node_id?: string | null;
  nombre_agente_amigable?: string | null;
  nombre_workflow?: string | null;
  user_id: string;
}
const mejoras_agentes = {
  tableName: 'mejoras_agentes',
  columns: ['id', 'agente_id', 'n8n_workflow_id', 'explicacion_mejoras', 'estado', 'created_at', 'updated_at', 'system_prompt_original', 'user_prompt_original', 'system_prompt_mejorado', 'user_prompt_mejorado', 'set_node_id', 'nombre_agente_amigable', 'nombre_workflow', 'user_id'],
  requiredForInsert: ['explicacion_mejoras', 'user_id'],
  primaryKey: 'id',
  foreignKeys: {
    agente_id: { table: 'agentes', column: 'id', $type: null as unknown as Agentes },
    user_id: { table: 'usuarios', column: 'auth_user_id', $type: null as unknown as Usuarios },
  },
  $type: null as unknown as MejorasAgentes,
  $input: null as unknown as MejorasAgentesInput
} as const;

// Table notificaciones
export interface Notificaciones {
  id: string;
  usuario_id: string;
  titulo: string;
  mensaje: string;
  url_destino: string | null;
  estado: string;
  tipo_notificacion: string;
  created_at: Date;
}
export interface NotificacionesInput {
  id?: string;
  usuario_id: string;
  titulo: string;
  mensaje: string;
  url_destino?: string | null;
  estado?: string;
  tipo_notificacion: string;
  created_at?: Date;
}
const notificaciones = {
  tableName: 'notificaciones',
  columns: ['id', 'usuario_id', 'titulo', 'mensaje', 'url_destino', 'estado', 'tipo_notificacion', 'created_at'],
  requiredForInsert: ['usuario_id', 'titulo', 'mensaje', 'tipo_notificacion'],
  primaryKey: 'id',
  foreignKeys: { usuario_id: { table: 'usuarios', column: 'id', $type: null as unknown as Usuarios }, },
  $type: null as unknown as Notificaciones,
  $input: null as unknown as NotificacionesInput
} as const;

// Table oportunidades
export interface Oportunidades {
  id: string;
  titulo: string;
  descripcion: string | null;
  empresa_id: string | null;
  persona_contacto_id: string | null;
  valor_estimado: number | null;
  moneda: string | null;
  etapa: string | null;
  probabilidad_cierre: number | null;
  fecha_cierre_estimada: Date | null;
  fecha_cierre_real: Date | null;
  motivo_perdida: string | null;
  documento_url: string | null;
  asignado_usuario_id: string | null;
  created_at: Date | null;
  updated_at: Date | null;
}
export interface OportunidadesInput {
  id?: string;
  titulo: string;
  descripcion?: string | null;
  empresa_id?: string | null;
  persona_contacto_id?: string | null;
  valor_estimado?: number | null;
  moneda?: string | null;
  etapa?: string | null;
  probabilidad_cierre?: number | null;
  fecha_cierre_estimada?: Date | null;
  fecha_cierre_real?: Date | null;
  motivo_perdida?: string | null;
  documento_url?: string | null;
  asignado_usuario_id?: string | null;
  created_at?: Date | null;
  updated_at?: Date | null;
}
const oportunidades = {
  tableName: 'oportunidades',
  columns: ['id', 'titulo', 'descripcion', 'empresa_id', 'persona_contacto_id', 'valor_estimado', 'moneda', 'etapa', 'probabilidad_cierre', 'fecha_cierre_estimada', 'fecha_cierre_real', 'motivo_perdida', 'documento_url', 'asignado_usuario_id', 'created_at', 'updated_at'],
  requiredForInsert: ['titulo'],
  primaryKey: 'id',
  foreignKeys: {
    empresa_id: { table: 'empresas', column: 'id', $type: null as unknown as Empresas },
    persona_contacto_id: { table: 'personas', column: 'id', $type: null as unknown as Personas },
    asignado_usuario_id: { table: 'usuarios', column: 'id', $type: null as unknown as Usuarios },
  },
  $type: null as unknown as Oportunidades,
  $input: null as unknown as OportunidadesInput
} as const;

// Table personas
export interface Personas {
  id: string;
  empresa_id: string | null;
  departamento_id: string | null;
  nombre: string;
  apellidos: string | null;
  email: string | null;
  telefono: string | null;
  cargo: string | null;
  tipo: string[];
  es_decision_maker: boolean | null;
  entrevistado: boolean | null;
  fecha_entrevista: Date | null;
  fecha_alta: Date | null;
  fecha_baja: Date | null;
  activo: boolean | null;
  info_adicional: string | null;
  created_at: Date | null;
  updated_at: Date | null;
  responsable_departamento: boolean | null;
  linkedin_url: string | null;
  /** Enlaza el registro de la persona con su cuenta de usuario en el portal. */
  usuario_id: string | null;
}
export interface PersonasInput {
  id?: string;
  empresa_id?: string | null;
  departamento_id?: string | null;
  nombre: string;
  apellidos?: string | null;
  email?: string | null;
  telefono?: string | null;
  cargo?: string | null;
  tipo: string[];
  es_decision_maker?: boolean | null;
  entrevistado?: boolean | null;
  fecha_entrevista?: Date | null;
  fecha_alta?: Date | null;
  fecha_baja?: Date | null;
  activo?: boolean | null;
  info_adicional?: string | null;
  created_at?: Date | null;
  updated_at?: Date | null;
  responsable_departamento?: boolean | null;
  linkedin_url?: string | null;
  /** Enlaza el registro de la persona con su cuenta de usuario en el portal. */
  usuario_id?: string | null;
}
const personas = {
  tableName: 'personas',
  columns: ['id', 'empresa_id', 'departamento_id', 'nombre', 'apellidos', 'email', 'telefono', 'cargo', 'tipo', 'es_decision_maker', 'entrevistado', 'fecha_entrevista', 'fecha_alta', 'fecha_baja', 'activo', 'info_adicional', 'created_at', 'updated_at', 'responsable_departamento', 'linkedin_url', 'usuario_id'],
  requiredForInsert: ['nombre', 'tipo'],
  primaryKey: 'id',
  foreignKeys: {
    empresa_id: { table: 'empresas', column: 'id', $type: null as unknown as Empresas },
    departamento_id: { table: 'departamentos', column: 'id', $type: null as unknown as Departamentos },
    usuario_id: { table: 'usuarios', column: 'id', $type: null as unknown as Usuarios },
  },
  $type: null as unknown as Personas,
  $input: null as unknown as PersonasInput
} as const;

// Table plantillas_tareas
export interface PlantillasTareas {
  id: string;
  titulo_plantilla: string;
  descripcion_base: string | null;
  duracion_estimada_horas: number | null;
  prioridad_predeterminada: prioridad_general_enum | null;
  info_adicional_plantilla: string | null;
  created_at: Date;
  updated_at: Date;
}
export interface PlantillasTareasInput {
  id?: string;
  titulo_plantilla: string;
  descripcion_base?: string | null;
  duracion_estimada_horas?: number | null;
  prioridad_predeterminada?: prioridad_general_enum | null;
  info_adicional_plantilla?: string | null;
  created_at?: Date;
  updated_at?: Date;
}
const plantillas_tareas = {
  tableName: 'plantillas_tareas',
  columns: ['id', 'titulo_plantilla', 'descripcion_base', 'duracion_estimada_horas', 'prioridad_predeterminada', 'info_adicional_plantilla', 'created_at', 'updated_at'],
  requiredForInsert: ['titulo_plantilla'],
  primaryKey: 'id',
  foreignKeys: {},
  $type: null as unknown as PlantillasTareas,
  $input: null as unknown as PlantillasTareasInput
} as const;

// Table preguntas
/** Gestiona preguntas para usuarios internos y clientes, con su estado y respuestas. */
export interface Preguntas {
  id: string;
  titulo: string;
  estado: pregunta_estado;
  visibilidad: pregunta_visibilidad;
  destinatario_usuario_id: string | null;
  origen_reunion_id: string | null;
  origen_proceso_id: string | null;
  respuesta_texto: string | null;
  respuesta_grabacion_id: string | null;
  info_adicional: string | null;
  created_at: Date;
  updated_at: Date;
}
/** Gestiona preguntas para usuarios internos y clientes, con su estado y respuestas. */
export interface PreguntasInput {
  id?: string;
  titulo: string;
  estado?: pregunta_estado;
  visibilidad?: pregunta_visibilidad;
  destinatario_usuario_id?: string | null;
  origen_reunion_id?: string | null;
  origen_proceso_id?: string | null;
  respuesta_texto?: string | null;
  respuesta_grabacion_id?: string | null;
  info_adicional?: string | null;
  created_at?: Date;
  updated_at?: Date;
}
const preguntas = {
  tableName: 'preguntas',
  columns: ['id', 'titulo', 'estado', 'visibilidad', 'destinatario_usuario_id', 'origen_reunion_id', 'origen_proceso_id', 'respuesta_texto', 'respuesta_grabacion_id', 'info_adicional', 'created_at', 'updated_at'],
  requiredForInsert: ['titulo'],
  primaryKey: 'id',
  foreignKeys: {
    destinatario_usuario_id: { table: 'usuarios', column: 'id', $type: null as unknown as Usuarios },
    origen_reunion_id: { table: 'reuniones', column: 'id', $type: null as unknown as Reuniones },
    origen_proceso_id: { table: 'procesos_clientes', column: 'id', $type: null as unknown as ProcesosClientes },
    respuesta_grabacion_id: { table: 'grabaciones', column: 'id', $type: null as unknown as Grabaciones },
  },
  $type: null as unknown as Preguntas,
  $input: null as unknown as PreguntasInput
} as const;

// Table procesos
export interface Procesos {
  id: string;
  empresa_id: string;
  departamento_id: string | null;
  nombre: string;
  descripcion: string | null;
  es_repetitivo: boolean | null;
  es_cuello_botella: boolean | null;
  es_manual: boolean | null;
  valor_negocio: string | null;
  complejidad_automatizacion: string | null;
  prioridad_automatizacion: string | null;
  tiempo_estimado_manual: number | null;
  frecuencia: string | null;
  created_at: Date | null;
  updated_at: Date | null;
  info_adicional: string | null;
  persona_id: string | null;
  /** Array de herramientas y software utilizados en este proceso */
  herramientas_utilizadas: Json | null;
  tipo_proceso: tipo_proceso_enum | null;
  proceso_plantilla_origen_id: string | null;
  reunion_origen_id: string | null;
}
export interface ProcesosInput {
  id?: string;
  empresa_id: string;
  departamento_id?: string | null;
  nombre: string;
  descripcion?: string | null;
  es_repetitivo?: boolean | null;
  es_cuello_botella?: boolean | null;
  es_manual?: boolean | null;
  valor_negocio?: string | null;
  complejidad_automatizacion?: string | null;
  prioridad_automatizacion?: string | null;
  tiempo_estimado_manual?: number | null;
  frecuencia?: string | null;
  created_at?: Date | null;
  updated_at?: Date | null;
  info_adicional?: string | null;
  persona_id?: string | null;
  /** Array de herramientas y software utilizados en este proceso */
  herramientas_utilizadas?: Json | null;
  tipo_proceso?: tipo_proceso_enum | null;
  proceso_plantilla_origen_id?: string | null;
  reunion_origen_id?: string | null;
}
const procesos = {
  tableName: 'procesos',
  columns: ['id', 'empresa_id', 'departamento_id', 'nombre', 'descripcion', 'es_repetitivo', 'es_cuello_botella', 'es_manual', 'valor_negocio', 'complejidad_automatizacion', 'prioridad_automatizacion', 'tiempo_estimado_manual', 'frecuencia', 'created_at', 'updated_at', 'info_adicional', 'persona_id', 'herramientas_utilizadas', 'tipo_proceso', 'proceso_plantilla_origen_id', 'reunion_origen_id'],
  requiredForInsert: ['empresa_id', 'nombre'],
  primaryKey: 'id',
  foreignKeys: {
    empresa_id: { table: 'empresas', column: 'id', $type: null as unknown as Empresas },
    departamento_id: { table: 'departamentos', column: 'id', $type: null as unknown as Departamentos },
    persona_id: { table: 'personas', column: 'id', $type: null as unknown as Personas },
    proceso_plantilla_origen_id: { table: 'procesos_plantillas', column: 'id', $type: null as unknown as ProcesosPlantillas },
    reunion_origen_id: { table: 'reuniones', column: 'id', $type: null as unknown as Reuniones },
  },
  $type: null as unknown as Procesos,
  $input: null as unknown as ProcesosInput
} as const;

// Table procesos_clientes
export interface ProcesosClientes {
  id: string;
  empresa_cliente_id: string;
  nombre: string;
  descripcion: string | null;
  estado_analisis: estado_proceso_cliente_enum | null;
  info_adicional: string | null;
  created_at: Date;
  updated_at: Date;
  reunion_origen_id: string | null;
  es_repetitivo: boolean | null;
  es_cuello_botella: boolean | null;
  es_manual: boolean | null;
  valor_negocio_cliente: string | null;
  complejidad_automatizacion_aceleralia: tipo_complejidad_automatizacion | null;
  prioridad_automatizacion_aceleralia: tipo_prioridad_automatizacion | null;
  duracion_minutos_por_ejecucion: number | null;
  frecuencia_periodo: tipo_frecuencia_periodo | null;
  herramientas_utilizadas_cliente: Json | null;
  frecuencia_ocurrencias: number | null;
}
export interface ProcesosClientesInput {
  id?: string;
  empresa_cliente_id: string;
  nombre: string;
  descripcion?: string | null;
  estado_analisis?: estado_proceso_cliente_enum | null;
  info_adicional?: string | null;
  created_at?: Date;
  updated_at?: Date;
  reunion_origen_id?: string | null;
  es_repetitivo?: boolean | null;
  es_cuello_botella?: boolean | null;
  es_manual?: boolean | null;
  valor_negocio_cliente?: string | null;
  complejidad_automatizacion_aceleralia?: tipo_complejidad_automatizacion | null;
  prioridad_automatizacion_aceleralia?: tipo_prioridad_automatizacion | null;
  duracion_minutos_por_ejecucion?: number | null;
  frecuencia_periodo?: tipo_frecuencia_periodo | null;
  herramientas_utilizadas_cliente?: Json | null;
  frecuencia_ocurrencias?: number | null;
}
const procesos_clientes = {
  tableName: 'procesos_clientes',
  columns: ['id', 'empresa_cliente_id', 'nombre', 'descripcion', 'estado_analisis', 'info_adicional', 'created_at', 'updated_at', 'reunion_origen_id', 'es_repetitivo', 'es_cuello_botella', 'es_manual', 'valor_negocio_cliente', 'complejidad_automatizacion_aceleralia', 'prioridad_automatizacion_aceleralia', 'duracion_minutos_por_ejecucion', 'frecuencia_periodo', 'herramientas_utilizadas_cliente', 'frecuencia_ocurrencias'],
  requiredForInsert: ['empresa_cliente_id', 'nombre'],
  primaryKey: 'id',
  foreignKeys: {
    empresa_cliente_id: { table: 'empresas', column: 'id', $type: null as unknown as Empresas },
    reunion_origen_id: { table: 'reuniones', column: 'id', $type: null as unknown as Reuniones },
  },
  $type: null as unknown as ProcesosClientes,
  $input: null as unknown as ProcesosClientesInput
} as const;

// Table procesos_clientes_departamentos
export interface ProcesosClientesDepartamentos {
  proceso_cliente_id: string;
  departamento_id: string;
  created_at: Date;
}
export interface ProcesosClientesDepartamentosInput {
  proceso_cliente_id: string;
  departamento_id: string;
  created_at?: Date;
}
const procesos_clientes_departamentos = {
  tableName: 'procesos_clientes_departamentos',
  columns: ['proceso_cliente_id', 'departamento_id', 'created_at'],
  requiredForInsert: ['proceso_cliente_id', 'departamento_id'],
  primaryKey: 'proceso_cliente_id',
  foreignKeys: {
    proceso_cliente_id: { table: 'procesos_clientes', column: 'id', $type: null as unknown as ProcesosClientes },
    departamento_id: { table: 'departamentos', column: 'id', $type: null as unknown as Departamentos },
  },
  $type: null as unknown as ProcesosClientesDepartamentos,
  $input: null as unknown as ProcesosClientesDepartamentosInput
} as const;

// Table procesos_clientes_responsables
export interface ProcesosClientesResponsables {
  id: string;
  proceso_cliente_id: string;
  persona_cliente_id: string;
  created_at: Date;
  updated_at: Date;
}
export interface ProcesosClientesResponsablesInput {
  id?: string;
  proceso_cliente_id: string;
  persona_cliente_id: string;
  created_at?: Date;
  updated_at?: Date;
}
const procesos_clientes_responsables = {
  tableName: 'procesos_clientes_responsables',
  columns: ['id', 'proceso_cliente_id', 'persona_cliente_id', 'created_at', 'updated_at'],
  requiredForInsert: ['proceso_cliente_id', 'persona_cliente_id'],
  primaryKey: 'id',
  foreignKeys: {
    proceso_cliente_id: { table: 'procesos_clientes', column: 'id', $type: null as unknown as ProcesosClientes },
    persona_cliente_id: { table: 'personas', column: 'id', $type: null as unknown as Personas },
  },
  $type: null as unknown as ProcesosClientesResponsables,
  $input: null as unknown as ProcesosClientesResponsablesInput
} as const;

// Table procesos_fuentes_informacion
/** Almacena las fuentes de información requeridas para ejecutar un proceso de cliente, quién la posee y en qué formato se encuentra. */
export interface ProcesosFuentesInformacion {
  /** Identificador único para la entrada de la fuente de información. */
  id: string;
  /** Enlaza con la tabla procesos_clientes. */
  proceso_cliente_id: string;
  /** El nombre de la información necesaria (ej: "Lista de Pedidos de Clientes"). */
  nombre_informacion: string;
  /** Una breve descripción de la información. */
  descripcion: string | null;
  /** Enlaza con la tabla personas. Identifica quién tiene la información. */
  persona_id: string;
  /** Campo de texto libre que describe el formato (ej: "Archivo Excel", "Papel físico"). */
  formato: string;
  /** URL al archivo adjunto opcional en el almacenamiento. */
  url_adjunto: string | null;
  /** El estado de la fuente de información: valido, pendiente_revision, obsoleto. */
  estado: procesos_fuentes_informacion_estado;
  /** La fecha en que esta fuente fue revisada o actualizada por última vez. */
  fecha_ultima_revision: Date | null;
  created_at: Date;
  updated_at: Date;
}
/** Almacena las fuentes de información requeridas para ejecutar un proceso de cliente, quién la posee y en qué formato se encuentra. */
export interface ProcesosFuentesInformacionInput {
  /** Identificador único para la entrada de la fuente de información. */
  id?: string;
  /** Enlaza con la tabla procesos_clientes. */
  proceso_cliente_id: string;
  /** El nombre de la información necesaria (ej: "Lista de Pedidos de Clientes"). */
  nombre_informacion: string;
  /** Una breve descripción de la información. */
  descripcion?: string | null;
  /** Enlaza con la tabla personas. Identifica quién tiene la información. */
  persona_id: string;
  /** Campo de texto libre que describe el formato (ej: "Archivo Excel", "Papel físico"). */
  formato: string;
  /** URL al archivo adjunto opcional en el almacenamiento. */
  url_adjunto?: string | null;
  /** El estado de la fuente de información: valido, pendiente_revision, obsoleto. */
  estado: procesos_fuentes_informacion_estado;
  /** La fecha en que esta fuente fue revisada o actualizada por última vez. */
  fecha_ultima_revision?: Date | null;
  created_at?: Date;
  updated_at?: Date;
}
const procesos_fuentes_informacion = {
  tableName: 'procesos_fuentes_informacion',
  columns: ['id', 'proceso_cliente_id', 'nombre_informacion', 'descripcion', 'persona_id', 'formato', 'url_adjunto', 'estado', 'fecha_ultima_revision', 'created_at', 'updated_at'],
  requiredForInsert: ['proceso_cliente_id', 'nombre_informacion', 'persona_id', 'formato', 'estado'],
  primaryKey: 'id',
  foreignKeys: {
    proceso_cliente_id: { table: 'procesos_clientes', column: 'id', $type: null as unknown as ProcesosClientes },
    persona_id: { table: 'personas', column: 'id', $type: null as unknown as Personas },
  },
  $type: null as unknown as ProcesosFuentesInformacion,
  $input: null as unknown as ProcesosFuentesInformacionInput
} as const;

// Table procesos_plantillas
export interface ProcesosPlantillas {
  id: string;
  nombre_plantilla: string;
  descripcion_plantilla: string | null;
  objetivo_plantilla: string | null;
  info_adicional_plantilla: string | null;
  created_at: Date;
  updated_at: Date;
}
export interface ProcesosPlantillasInput {
  id?: string;
  nombre_plantilla: string;
  descripcion_plantilla?: string | null;
  objetivo_plantilla?: string | null;
  info_adicional_plantilla?: string | null;
  created_at?: Date;
  updated_at?: Date;
}
const procesos_plantillas = {
  tableName: 'procesos_plantillas',
  columns: ['id', 'nombre_plantilla', 'descripcion_plantilla', 'objetivo_plantilla', 'info_adicional_plantilla', 'created_at', 'updated_at'],
  requiredForInsert: ['nombre_plantilla'],
  primaryKey: 'id',
  foreignKeys: {},
  $type: null as unknown as ProcesosPlantillas,
  $input: null as unknown as ProcesosPlantillasInput
} as const;

// Table procesos_tareas_plantilla
export interface ProcesosTareasPlantilla {
  id: string;
  proceso_plantilla_id: string;
  plantilla_tarea_id: string;
  orden_en_proceso: number;
  dias_desplazamiento: number | null;
  es_obligatoria: boolean | null;
  notas_especificas_proceso: string | null;
  created_at: Date;
  updated_at: Date;
}
export interface ProcesosTareasPlantillaInput {
  id?: string;
  proceso_plantilla_id: string;
  plantilla_tarea_id: string;
  orden_en_proceso: number;
  dias_desplazamiento?: number | null;
  es_obligatoria?: boolean | null;
  notas_especificas_proceso?: string | null;
  created_at?: Date;
  updated_at?: Date;
}
const procesos_tareas_plantilla = {
  tableName: 'procesos_tareas_plantilla',
  columns: ['id', 'proceso_plantilla_id', 'plantilla_tarea_id', 'orden_en_proceso', 'dias_desplazamiento', 'es_obligatoria', 'notas_especificas_proceso', 'created_at', 'updated_at'],
  requiredForInsert: ['proceso_plantilla_id', 'plantilla_tarea_id', 'orden_en_proceso'],
  primaryKey: 'id',
  foreignKeys: {
    proceso_plantilla_id: { table: 'procesos_plantillas', column: 'id', $type: null as unknown as ProcesosPlantillas },
    plantilla_tarea_id: { table: 'plantillas_tareas', column: 'id', $type: null as unknown as PlantillasTareas },
  },
  $type: null as unknown as ProcesosTareasPlantilla,
  $input: null as unknown as ProcesosTareasPlantillaInput
} as const;

// Table proyecto_personas
export interface ProyectoPersonas {
  id: string;
  proyecto_id: string | null;
  persona_id: string | null;
  rol: string | null;
  asignado_desde: Date | null;
  asignado_hasta: Date | null;
  porcentaje_dedicacion: number | null;
  es_responsable: boolean | null;
  info_adicional: string | null;
  created_at: Date | null;
  updated_at: Date | null;
}
export interface ProyectoPersonasInput {
  id?: string;
  proyecto_id?: string | null;
  persona_id?: string | null;
  rol?: string | null;
  asignado_desde?: Date | null;
  asignado_hasta?: Date | null;
  porcentaje_dedicacion?: number | null;
  es_responsable?: boolean | null;
  info_adicional?: string | null;
  created_at?: Date | null;
  updated_at?: Date | null;
}
const proyecto_personas = {
  tableName: 'proyecto_personas',
  columns: ['id', 'proyecto_id', 'persona_id', 'rol', 'asignado_desde', 'asignado_hasta', 'porcentaje_dedicacion', 'es_responsable', 'info_adicional', 'created_at', 'updated_at'],
  requiredForInsert: [],
  primaryKey: 'id',
  foreignKeys: {
    proyecto_id: { table: 'proyectos', column: 'id', $type: null as unknown as Proyectos },
    persona_id: { table: 'personas', column: 'id', $type: null as unknown as Personas },
  },
  $type: null as unknown as ProyectoPersonas,
  $input: null as unknown as ProyectoPersonasInput
} as const;

// Table proyectos
export interface Proyectos {
  id: string;
  nombre: string;
  descripcion: string | null;
  objetivo: string | null;
  estado: string;
  fecha_inicio: Date | null;
  fecha_fin_estimada: Date | null;
  fecha_fin_real: Date | null;
  presupuesto: number | null;
  created_at: Date | null;
  updated_at: Date | null;
  responsable_persona_id: string | null;
  info_adicional: string | null;
  prioridad: string | null;
  progreso: number | null;
  responsable_usuario_id: string | null;
}
export interface ProyectosInput {
  id?: string;
  nombre: string;
  descripcion?: string | null;
  objetivo?: string | null;
  estado: string;
  fecha_inicio?: Date | null;
  fecha_fin_estimada?: Date | null;
  fecha_fin_real?: Date | null;
  presupuesto?: number | null;
  created_at?: Date | null;
  updated_at?: Date | null;
  responsable_persona_id?: string | null;
  info_adicional?: string | null;
  prioridad?: string | null;
  progreso?: number | null;
  responsable_usuario_id?: string | null;
}
const proyectos = {
  tableName: 'proyectos',
  columns: ['id', 'nombre', 'descripcion', 'objetivo', 'estado', 'fecha_inicio', 'fecha_fin_estimada', 'fecha_fin_real', 'presupuesto', 'created_at', 'updated_at', 'responsable_persona_id', 'info_adicional', 'prioridad', 'progreso', 'responsable_usuario_id'],
  requiredForInsert: ['nombre', 'estado'],
  primaryKey: 'id',
  foreignKeys: {
    responsable_persona_id: { table: 'personas', column: 'id', $type: null as unknown as Personas },
    responsable_usuario_id: { table: 'usuarios', column: 'id', $type: null as unknown as Usuarios },
  },
  $type: null as unknown as Proyectos,
  $input: null as unknown as ProyectosInput
} as const;

// Table proyectos_empresas
export interface ProyectosEmpresas {
  proyecto_id: string;
  empresa_id: string;
}
export interface ProyectosEmpresasInput {
  proyecto_id: string;
  empresa_id: string;
}
const proyectos_empresas = {
  tableName: 'proyectos_empresas',
  columns: ['proyecto_id', 'empresa_id'],
  requiredForInsert: ['proyecto_id', 'empresa_id'],
  primaryKey: 'proyecto_id',
  foreignKeys: {
    proyecto_id: { table: 'proyectos', column: 'id', $type: null as unknown as Proyectos },
    empresa_id: { table: 'empresas', column: 'id', $type: null as unknown as Empresas },
  },
  $type: null as unknown as ProyectosEmpresas,
  $input: null as unknown as ProyectosEmpresasInput
} as const;

// Table proyectos_procesos
export interface ProyectosProcesos {
  id: string;
  proyecto_id: string;
  proceso_id: string;
  created_at: Date;
}
export interface ProyectosProcesosInput {
  id?: string;
  proyecto_id: string;
  proceso_id: string;
  created_at?: Date;
}
const proyectos_procesos = {
  tableName: 'proyectos_procesos',
  columns: ['id', 'proyecto_id', 'proceso_id', 'created_at'],
  requiredForInsert: ['proyecto_id', 'proceso_id'],
  primaryKey: 'id',
  foreignKeys: {
    proyecto_id: { table: 'proyectos', column: 'id', $type: null as unknown as Proyectos },
    proceso_id: { table: 'procesos', column: 'id', $type: null as unknown as Procesos },
  },
  $type: null as unknown as ProyectosProcesos,
  $input: null as unknown as ProyectosProcesosInput
} as const;

// Table retos_subtareas
/** Desglose de retos agrupados en acciones concretas y verificables. */
export interface RetosSubtareas {
  id: string;
  reto_usuario_id: string;
  titulo: string;
  descripcion: string | null;
  tipo_accion: reto_tipo_accion;
  entidad_relacionada_id: string;
  entidad_relacionada_tipo: grabacion_entidad_tipo;
  estado: reto_estado;
  created_at: Date;
  updated_at: Date;
}
/** Desglose de retos agrupados en acciones concretas y verificables. */
export interface RetosSubtareasInput {
  id?: string;
  reto_usuario_id: string;
  titulo: string;
  descripcion?: string | null;
  tipo_accion: reto_tipo_accion;
  entidad_relacionada_id: string;
  entidad_relacionada_tipo: grabacion_entidad_tipo;
  estado?: reto_estado;
  created_at?: Date;
  updated_at?: Date;
}
const retos_subtareas = {
  tableName: 'retos_subtareas',
  columns: ['id', 'reto_usuario_id', 'titulo', 'descripcion', 'tipo_accion', 'entidad_relacionada_id', 'entidad_relacionada_tipo', 'estado', 'created_at', 'updated_at'],
  requiredForInsert: ['reto_usuario_id', 'titulo', 'tipo_accion', 'entidad_relacionada_id', 'entidad_relacionada_tipo'],
  primaryKey: 'id',
  foreignKeys: { reto_usuario_id: { table: 'retos_usuarios', column: 'id', $type: null as unknown as RetosUsuarios }, },
  $type: null as unknown as RetosSubtareas,
  $input: null as unknown as RetosSubtareasInput
} as const;

// Table retos_usuarios
/** Almacena los retos de gamificación asignados a los usuarios. */
export interface RetosUsuarios {
  id: string;
  usuario_id: string;
  titulo: string;
  descripcion: string | null;
  puntos_recompensa: number;
  estado: reto_estado;
  prioridad: number | null;
  url_destino: string | null;
  created_at: Date;
  updated_at: Date;
}
/** Almacena los retos de gamificación asignados a los usuarios. */
export interface RetosUsuariosInput {
  id?: string;
  usuario_id: string;
  titulo: string;
  descripcion?: string | null;
  puntos_recompensa?: number;
  estado?: reto_estado;
  prioridad?: number | null;
  url_destino?: string | null;
  created_at?: Date;
  updated_at?: Date;
}
const retos_usuarios = {
  tableName: 'retos_usuarios',
  columns: ['id', 'usuario_id', 'titulo', 'descripcion', 'puntos_recompensa', 'estado', 'prioridad', 'url_destino', 'created_at', 'updated_at'],
  requiredForInsert: ['usuario_id', 'titulo'],
  primaryKey: 'id',
  foreignKeys: { usuario_id: { table: 'usuarios', column: 'id', $type: null as unknown as Usuarios }, },
  $type: null as unknown as RetosUsuarios,
  $input: null as unknown as RetosUsuariosInput
} as const;

// Table reunion_empresas_asociadas
/** Associates meetings with one or more companies (empresas). */
export interface ReunionEmpresasAsociadas {
  reunion_id: string;
  empresa_id: string;
  created_at: Date;
}
/** Associates meetings with one or more companies (empresas). */
export interface ReunionEmpresasAsociadasInput {
  reunion_id: string;
  empresa_id: string;
  created_at?: Date;
}
const reunion_empresas_asociadas = {
  tableName: 'reunion_empresas_asociadas',
  columns: ['reunion_id', 'empresa_id', 'created_at'],
  requiredForInsert: ['reunion_id', 'empresa_id'],
  primaryKey: 'reunion_id',
  foreignKeys: {
    reunion_id: { table: 'reuniones', column: 'id', $type: null as unknown as Reuniones },
    empresa_id: { table: 'empresas', column: 'id', $type: null as unknown as Empresas },
  },
  $type: null as unknown as ReunionEmpresasAsociadas,
  $input: null as unknown as ReunionEmpresasAsociadasInput
} as const;

// Table reunion_personas_asociadas
/** Associates meetings with one or more people (personas). */
export interface ReunionPersonasAsociadas {
  reunion_id: string;
  persona_id: string;
  created_at: Date;
}
/** Associates meetings with one or more people (personas). */
export interface ReunionPersonasAsociadasInput {
  reunion_id: string;
  persona_id: string;
  created_at?: Date;
}
const reunion_personas_asociadas = {
  tableName: 'reunion_personas_asociadas',
  columns: ['reunion_id', 'persona_id', 'created_at'],
  requiredForInsert: ['reunion_id', 'persona_id'],
  primaryKey: 'reunion_id',
  foreignKeys: {
    reunion_id: { table: 'reuniones', column: 'id', $type: null as unknown as Reuniones },
    persona_id: { table: 'personas', column: 'id', $type: null as unknown as Personas },
  },
  $type: null as unknown as ReunionPersonasAsociadas,
  $input: null as unknown as ReunionPersonasAsociadasInput
} as const;

// Table reunion_speaker_asignaciones
/** Maps generic speaker tags from a meeting's raw transcription to specific identified participants (users, personas, or lead contacts). */
export interface ReunionSpeakerAsignaciones {
  id: string;
  reunion_id: string;
  speaker_tag: string;
  /** Type of entity assigned as speaker: 'usuario', 'persona', 'lead_contacto'. */
  asignado_a_tipo: string;
  asignado_a_id: string;
  nombre_asignado: string | null;
  created_at: Date;
  /** Timestamp of the last update to the speaker assignment. */
  updated_at: Date;
}
/** Maps generic speaker tags from a meeting's raw transcription to specific identified participants (users, personas, or lead contacts). */
export interface ReunionSpeakerAsignacionesInput {
  id?: string;
  reunion_id: string;
  speaker_tag: string;
  /** Type of entity assigned as speaker: 'usuario', 'persona', 'lead_contacto'. */
  asignado_a_tipo: string;
  asignado_a_id: string;
  nombre_asignado?: string | null;
  created_at?: Date;
  /** Timestamp of the last update to the speaker assignment. */
  updated_at?: Date;
}
const reunion_speaker_asignaciones = {
  tableName: 'reunion_speaker_asignaciones',
  columns: ['id', 'reunion_id', 'speaker_tag', 'asignado_a_tipo', 'asignado_a_id', 'nombre_asignado', 'created_at', 'updated_at'],
  requiredForInsert: ['reunion_id', 'speaker_tag', 'asignado_a_tipo', 'asignado_a_id'],
  primaryKey: 'id',
  foreignKeys: { reunion_id: { table: 'reuniones', column: 'id', $type: null as unknown as Reuniones }, },
  $type: null as unknown as ReunionSpeakerAsignaciones,
  $input: null as unknown as ReunionSpeakerAsignacionesInput
} as const;

// Table reuniones
/** Stores information about processed meetings, including recordings, transcriptions, and AI-generated insights. */
export interface Reuniones {
  id: string;
  /** ID of the user who uploaded/created the meeting, references public.usuarios table. */
  user_id: string | null;
  titulo: string | null;
  observaciones_iniciales: string | null;
  /** Path or URL to the original meeting recording file in Supabase Storage. */
  url_grabacion_original: string | null;
  /** Publicly accessible URL for the recording, typically for n8n processing. */
  url_grabacion_publica: string | null;
  fecha_reunion: Date | null;
  transcripcion_raw: string | null;
  transcripcion_final: string | null;
  resumen: string | null;
  puntos_clave: Json | null;
  /** Current processing state of the meeting (e.g., pending_upload, pendiente_transcripcion, pendiente_asignacion_speakers, procesando_ia, completado, error). */
  estado_procesamiento: string;
  info_adicional: string | null;
  created_at: Date;
  updated_at: Date;
  entrevista: boolean | null;
  video: boolean | null;
  duracion_minutos: number | null;
}
/** Stores information about processed meetings, including recordings, transcriptions, and AI-generated insights. */
export interface ReunionesInput {
  id?: string;
  /** ID of the user who uploaded/created the meeting, references public.usuarios table. */
  user_id?: string | null;
  titulo?: string | null;
  observaciones_iniciales?: string | null;
  /** Path or URL to the original meeting recording file in Supabase Storage. */
  url_grabacion_original?: string | null;
  /** Publicly accessible URL for the recording, typically for n8n processing. */
  url_grabacion_publica?: string | null;
  fecha_reunion?: Date | null;
  transcripcion_raw?: string | null;
  transcripcion_final?: string | null;
  resumen?: string | null;
  puntos_clave?: Json | null;
  /** Current processing state of the meeting (e.g., pending_upload, pendiente_transcripcion, pendiente_asignacion_speakers, procesando_ia, completado, error). */
  estado_procesamiento?: string;
  info_adicional?: string | null;
  created_at?: Date;
  updated_at?: Date;
  entrevista?: boolean | null;
  video?: boolean | null;
  duracion_minutos?: number | null;
}
const reuniones = {
  tableName: 'reuniones',
  columns: ['id', 'user_id', 'titulo', 'observaciones_iniciales', 'url_grabacion_original', 'url_grabacion_publica', 'fecha_reunion', 'transcripcion_raw', 'transcripcion_final', 'resumen', 'puntos_clave', 'estado_procesamiento', 'info_adicional', 'created_at', 'updated_at', 'entrevista', 'video', 'duracion_minutos'],
  requiredForInsert: [],
  primaryKey: 'id',
  foreignKeys: { user_id: { table: 'usuarios', column: 'id', $type: null as unknown as Usuarios }, },
  $type: null as unknown as Reuniones,
  $input: null as unknown as ReunionesInput
} as const;

// Table sops_y_guias
export interface SopsYGuias {
  id: string;
  titulo: string;
  descripcion: string | null;
  contenido_markdown: string;
  dueno_sop_id: string | null;
  created_at: Date | null;
  updated_at: Date | null;
  type: sop_guia_type_enum | null;
}
export interface SopsYGuiasInput {
  id?: string;
  titulo: string;
  descripcion?: string | null;
  contenido_markdown: string;
  dueno_sop_id?: string | null;
  created_at?: Date | null;
  updated_at?: Date | null;
  type?: sop_guia_type_enum | null;
}
const sops_y_guias = {
  tableName: 'sops_y_guias',
  columns: ['id', 'titulo', 'descripcion', 'contenido_markdown', 'dueno_sop_id', 'created_at', 'updated_at', 'type'],
  requiredForInsert: ['titulo', 'contenido_markdown'],
  primaryKey: 'id',
  foreignKeys: { dueno_sop_id: { table: 'usuarios', column: 'id', $type: null as unknown as Usuarios }, },
  $type: null as unknown as SopsYGuias,
  $input: null as unknown as SopsYGuiasInput
} as const;

// Table tareas
export interface Tareas {
  id: string;
  proyecto_id: string;
  workflow_id: string | null;
  titulo: string;
  descripcion: string | null;
  fecha_vencimiento: Date | null;
  fecha_completado: Date | null;
  asignado_a: string | null;
  creado_por: string | null;
  created_at: Date | null;
  updated_at: Date | null;
  info_adicional: string | null;
  estado: estado_tarea;
  prioridad: prioridad_tarea | null;
  tarea_padre_id: string | null;
  urgencia: urgencia_tarea_enum | null;
  reunion_id: string | null;
  fecha_inicio: Date | null;
}
export interface TareasInput {
  id?: string;
  proyecto_id: string;
  workflow_id?: string | null;
  titulo: string;
  descripcion?: string | null;
  fecha_vencimiento?: Date | null;
  fecha_completado?: Date | null;
  asignado_a?: string | null;
  creado_por?: string | null;
  created_at?: Date | null;
  updated_at?: Date | null;
  info_adicional?: string | null;
  estado?: estado_tarea;
  prioridad?: prioridad_tarea | null;
  tarea_padre_id?: string | null;
  urgencia?: urgencia_tarea_enum | null;
  reunion_id?: string | null;
  fecha_inicio?: Date | null;
}
const tareas = {
  tableName: 'tareas',
  columns: ['id', 'proyecto_id', 'workflow_id', 'titulo', 'descripcion', 'fecha_vencimiento', 'fecha_completado', 'asignado_a', 'creado_por', 'created_at', 'updated_at', 'info_adicional', 'estado', 'prioridad', 'tarea_padre_id', 'urgencia', 'reunion_id', 'fecha_inicio'],
  requiredForInsert: ['proyecto_id', 'titulo'],
  primaryKey: 'id',
  foreignKeys: {
    proyecto_id: { table: 'proyectos', column: 'id', $type: null as unknown as Proyectos },
    workflow_id: { table: 'workflows', column: 'id', $type: null as unknown as Workflows },
    asignado_a: { table: 'usuarios', column: 'id', $type: null as unknown as Usuarios },
    creado_por: { table: 'usuarios', column: 'id', $type: null as unknown as Usuarios },
    tarea_padre_id: { table: 'tareas', column: 'id', $type: null as unknown as Tareas },
    reunion_id: { table: 'reuniones', column: 'id', $type: null as unknown as Reuniones },
  },
  $type: null as unknown as Tareas,
  $input: null as unknown as TareasInput
} as const;

// Table tareas_clientes
export interface TareasClientes {
  id: string;
  proceso_cliente_id: string;
  nombre_tarea_cliente: string;
  descripcion_tarea_cliente: string | null;
  duracion_minutos_por_ejecucion: number | null;
  frecuencia_periodo: tipo_frecuencia_periodo | null;
  es_manual_cliente: boolean | null;
  herramientas_utilizadas_cliente: Json | null;
  puntos_dolor_cliente: string | null;
  oportunidades_mejora_cliente: string | null;
  info_adicional: string | null;
  created_at: Date;
  updated_at: Date;
  frecuencia_ocurrencias: number | null;
}
export interface TareasClientesInput {
  id?: string;
  proceso_cliente_id: string;
  nombre_tarea_cliente: string;
  descripcion_tarea_cliente?: string | null;
  duracion_minutos_por_ejecucion?: number | null;
  frecuencia_periodo?: tipo_frecuencia_periodo | null;
  es_manual_cliente?: boolean | null;
  herramientas_utilizadas_cliente?: Json | null;
  puntos_dolor_cliente?: string | null;
  oportunidades_mejora_cliente?: string | null;
  info_adicional?: string | null;
  created_at?: Date;
  updated_at?: Date;
  frecuencia_ocurrencias?: number | null;
}
const tareas_clientes = {
  tableName: 'tareas_clientes',
  columns: ['id', 'proceso_cliente_id', 'nombre_tarea_cliente', 'descripcion_tarea_cliente', 'duracion_minutos_por_ejecucion', 'frecuencia_periodo', 'es_manual_cliente', 'herramientas_utilizadas_cliente', 'puntos_dolor_cliente', 'oportunidades_mejora_cliente', 'info_adicional', 'created_at', 'updated_at', 'frecuencia_ocurrencias'],
  requiredForInsert: ['proceso_cliente_id', 'nombre_tarea_cliente'],
  primaryKey: 'id',
  foreignKeys: { proceso_cliente_id: { table: 'procesos_clientes', column: 'id', $type: null as unknown as ProcesosClientes }, },
  $type: null as unknown as TareasClientes,
  $input: null as unknown as TareasClientesInput
} as const;

// Table tareas_clientes_responsables
export interface TareasClientesResponsables {
  id: string;
  tarea_cliente_id: string;
  persona_cliente_id: string;
  created_at: Date;
  updated_at: Date;
}
export interface TareasClientesResponsablesInput {
  id?: string;
  tarea_cliente_id: string;
  persona_cliente_id: string;
  created_at?: Date;
  updated_at?: Date;
}
const tareas_clientes_responsables = {
  tableName: 'tareas_clientes_responsables',
  columns: ['id', 'tarea_cliente_id', 'persona_cliente_id', 'created_at', 'updated_at'],
  requiredForInsert: ['tarea_cliente_id', 'persona_cliente_id'],
  primaryKey: 'id',
  foreignKeys: {
    tarea_cliente_id: { table: 'tareas_clientes', column: 'id', $type: null as unknown as TareasClientes },
    persona_cliente_id: { table: 'personas', column: 'id', $type: null as unknown as Personas },
  },
  $type: null as unknown as TareasClientesResponsables,
  $input: null as unknown as TareasClientesResponsablesInput
} as const;

// Table tareas_empresas
export interface TareasEmpresas {
  tarea_id: string;
  empresa_id: string;
}
export interface TareasEmpresasInput {
  tarea_id: string;
  empresa_id: string;
}
const tareas_empresas = {
  tableName: 'tareas_empresas',
  columns: ['tarea_id', 'empresa_id'],
  requiredForInsert: ['tarea_id', 'empresa_id'],
  primaryKey: 'tarea_id',
  foreignKeys: {
    tarea_id: { table: 'tareas', column: 'id', $type: null as unknown as Tareas },
    empresa_id: { table: 'empresas', column: 'id', $type: null as unknown as Empresas },
  },
  $type: null as unknown as TareasEmpresas,
  $input: null as unknown as TareasEmpresasInput
} as const;

// Table tareas_etiquetas
export interface TareasEtiquetas {
  tarea_id: string;
  etiqueta_id: string;
}
export interface TareasEtiquetasInput {
  tarea_id: string;
  etiqueta_id: string;
}
const tareas_etiquetas = {
  tableName: 'tareas_etiquetas',
  columns: ['tarea_id', 'etiqueta_id'],
  requiredForInsert: ['tarea_id', 'etiqueta_id'],
  primaryKey: 'tarea_id',
  foreignKeys: {
    tarea_id: { table: 'tareas', column: 'id', $type: null as unknown as Tareas },
    etiqueta_id: { table: 'etiquetas', column: 'id', $type: null as unknown as Etiquetas },
  },
  $type: null as unknown as TareasEtiquetas,
  $input: null as unknown as TareasEtiquetasInput
} as const;

// Table thread_message_counts
export interface ThreadMessageCounts {
  thread_id: number | null;
  message_count: number | null;
  last_message_at: Date | null;
  first_message_at: Date | null;
}
export interface ThreadMessageCountsInput {
  thread_id?: number | null;
  message_count?: number | null;
  last_message_at?: Date | null;
  first_message_at?: Date | null;
}
const thread_message_counts = {
  tableName: 'thread_message_counts',
  columns: ['thread_id', 'message_count', 'last_message_at', 'first_message_at'],
  requiredForInsert: [],
  primaryKey: null,
  foreignKeys: {},
  $type: null as unknown as ThreadMessageCounts,
  $input: null as unknown as ThreadMessageCountsInput
} as const;

// Table threads
export interface Threads {
  thread_id: number;
  created_at: Date;
  content: string | null;
  type: string | null;
  from: string | null;
  message_id: number | null;
  agent_id: string | null;
  user_id: string | null;
  id: string;
  request_type: thread_request_type_enum | null;
  input_token_cost: number | null;
  output_token_cost: number | null;
}
export interface ThreadsInput {
  thread_id: number;
  created_at?: Date;
  content?: string | null;
  type?: string | null;
  from?: string | null;
  message_id?: number | null;
  agent_id?: string | null;
  user_id?: string | null;
  id?: string;
  request_type?: thread_request_type_enum | null;
  input_token_cost?: number | null;
  output_token_cost?: number | null;
}
const threads = {
  tableName: 'threads',
  columns: ['thread_id', 'created_at', 'content', 'type', 'from', 'message_id', 'agent_id', 'user_id', 'id', 'request_type', 'input_token_cost', 'output_token_cost'],
  requiredForInsert: ['thread_id'],
  primaryKey: 'id',
  foreignKeys: {
    agent_id: { table: 'agentes', column: 'id', $type: null as unknown as Agentes },
    user_id: { table: 'usuarios', column: 'id', $type: null as unknown as Usuarios },
  },
  $type: null as unknown as Threads,
  $input: null as unknown as ThreadsInput
} as const;

// Table threads_metadata
export interface ThreadsMetadata {
  thread_id: number;
  titulo: string | null;
  created_at: Date | null;
  updated_at: Date | null;
}
export interface ThreadsMetadataInput {
  thread_id: number;
  titulo?: string | null;
  created_at?: Date | null;
  updated_at?: Date | null;
}
const threads_metadata = {
  tableName: 'threads_metadata',
  columns: ['thread_id', 'titulo', 'created_at', 'updated_at'],
  requiredForInsert: ['thread_id'],
  primaryKey: 'thread_id',
  foreignKeys: {},
  $type: null as unknown as ThreadsMetadata,
  $input: null as unknown as ThreadsMetadataInput
} as const;

// Table threads_resumen
export interface ThreadsResumen {
  id: string;
  thread_id_procesado: string;
  titulo: string | null;
  resumen: string | null;
  tools_utilizadas: string | null;
  observaciones_agente: string | null;
  fecha_resumen: Date | null;
  db_modifications_summary: string | null;
}
export interface ThreadsResumenInput {
  id?: string;
  thread_id_procesado: string;
  titulo?: string | null;
  resumen?: string | null;
  tools_utilizadas?: string | null;
  observaciones_agente?: string | null;
  fecha_resumen?: Date | null;
  db_modifications_summary?: string | null;
}
const threads_resumen = {
  tableName: 'threads_resumen',
  columns: ['id', 'thread_id_procesado', 'titulo', 'resumen', 'tools_utilizadas', 'observaciones_agente', 'fecha_resumen', 'db_modifications_summary'],
  requiredForInsert: ['thread_id_procesado'],
  primaryKey: 'id',
  foreignKeys: {},
  $type: null as unknown as ThreadsResumen,
  $input: null as unknown as ThreadsResumenInput
} as const;

// Table ticket_mensajes
export interface TicketMensajes {
  id: string;
  ticket_id: string;
  usuario_id: string | null;
  contenido: string;
  tipo: string;
  es_privado: boolean | null;
  adjunto_url: string | null;
  created_at: Date | null;
}
export interface TicketMensajesInput {
  id?: string;
  ticket_id: string;
  usuario_id?: string | null;
  contenido: string;
  tipo: string;
  es_privado?: boolean | null;
  adjunto_url?: string | null;
  created_at?: Date | null;
}
const ticket_mensajes = {
  tableName: 'ticket_mensajes',
  columns: ['id', 'ticket_id', 'usuario_id', 'contenido', 'tipo', 'es_privado', 'adjunto_url', 'created_at'],
  requiredForInsert: ['ticket_id', 'contenido', 'tipo'],
  primaryKey: 'id',
  foreignKeys: {
    ticket_id: { table: 'tickets', column: 'id', $type: null as unknown as Tickets },
    usuario_id: { table: 'usuarios', column: 'id', $type: null as unknown as Usuarios },
  },
  $type: null as unknown as TicketMensajes,
  $input: null as unknown as TicketMensajesInput
} as const;

// Table tickets
export interface Tickets {
  id: string;
  empresa_id: string;
  proyecto_id: string | null;
  workflow_id: string | null;
  contacto_id: string | null;
  titulo: string;
  descripcion: string | null;
  detalles_tecnicos: string | null;
  reproducibilidad: string | null;
  estado: string;
  prioridad: string;
  fecha_resolucion: Date | null;
  asignado_a: string | null;
  resolucion_descripcion: string | null;
  canal_origen: string | null;
  created_at: Date | null;
  updated_at: Date | null;
}
export interface TicketsInput {
  id?: string;
  empresa_id: string;
  proyecto_id?: string | null;
  workflow_id?: string | null;
  contacto_id?: string | null;
  titulo: string;
  descripcion?: string | null;
  detalles_tecnicos?: string | null;
  reproducibilidad?: string | null;
  estado: string;
  prioridad: string;
  fecha_resolucion?: Date | null;
  asignado_a?: string | null;
  resolucion_descripcion?: string | null;
  canal_origen?: string | null;
  created_at?: Date | null;
  updated_at?: Date | null;
}
const tickets = {
  tableName: 'tickets',
  columns: ['id', 'empresa_id', 'proyecto_id', 'workflow_id', 'contacto_id', 'titulo', 'descripcion', 'detalles_tecnicos', 'reproducibilidad', 'estado', 'prioridad', 'fecha_resolucion', 'asignado_a', 'resolucion_descripcion', 'canal_origen', 'created_at', 'updated_at'],
  requiredForInsert: ['empresa_id', 'titulo', 'estado', 'prioridad'],
  primaryKey: 'id',
  foreignKeys: {
    empresa_id: { table: 'empresas', column: 'id', $type: null as unknown as Empresas },
    proyecto_id: { table: 'proyectos', column: 'id', $type: null as unknown as Proyectos },
    workflow_id: { table: 'workflows', column: 'id', $type: null as unknown as Workflows },
    asignado_a: { table: 'usuarios', column: 'id', $type: null as unknown as Usuarios },
  },
  $type: null as unknown as Tickets,
  $input: null as unknown as TicketsInput
} as const;

// Table tools
export interface Tools {
  id: string;
  tool_name: string;
  tool_description: string | null;
  tool_config: string;
  created_at: Date | null;
  updated_at: Date | null;
}
export interface ToolsInput {
  id?: string;
  tool_name: string;
  tool_description?: string | null;
  tool_config: string;
  created_at?: Date | null;
  updated_at?: Date | null;
}
const tools = {
  tableName: 'tools',
  columns: ['id', 'tool_name', 'tool_description', 'tool_config', 'created_at', 'updated_at'],
  requiredForInsert: ['tool_name', 'tool_config'],
  primaryKey: 'id',
  foreignKeys: {},
  $type: null as unknown as Tools,
  $input: null as unknown as ToolsInput
} as const;

// Table usuarios
export interface Usuarios {
  id: string;
  email: string;
  nombre: string;
  rol: string;
  empresa_id: string | null;
  avatar_url: string | null;
  ultimo_acceso: Date | null;
  created_at: Date | null;
  actualizado_en: Date | null;
  info_adicional: string | null;
  apellidos: string | null;
  auth_user_id: string;
  puntos: number;
  racha_actual: number | null;
  ultima_actividad_racha: Date | null;
}
export interface UsuariosInput {
  id?: string;
  email: string;
  nombre: string;
  rol: string;
  empresa_id?: string | null;
  avatar_url?: string | null;
  ultimo_acceso?: Date | null;
  created_at?: Date | null;
  actualizado_en?: Date | null;
  info_adicional?: string | null;
  apellidos?: string | null;
  auth_user_id: string;
  puntos?: number;
  racha_actual?: number | null;
  ultima_actividad_racha?: Date | null;
}
const usuarios = {
  tableName: 'usuarios',
  columns: ['id', 'email', 'nombre', 'rol', 'empresa_id', 'avatar_url', 'ultimo_acceso', 'created_at', 'actualizado_en', 'info_adicional', 'apellidos', 'auth_user_id', 'puntos', 'racha_actual', 'ultima_actividad_racha'],
  requiredForInsert: ['email', 'nombre', 'rol', 'auth_user_id'],
  primaryKey: 'id',
  foreignKeys: {
    empresa_id: { table: 'empresas', column: 'id', $type: null as unknown as Empresas },
    auth_user_id: { table: 'users', column: 'id', $type: null as unknown /* users */ },
  },
  $type: null as unknown as Usuarios,
  $input: null as unknown as UsuariosInput
} as const;

// Table workflow_errores
export interface WorkflowErrores {
  id: string;
  workflow_id: string | null;
  tipo_error: string;
  mensaje: string;
  detalles: Json | null;
  stack_trace: string | null;
  fecha_error: Date | null;
  estado: string;
  notificado: boolean | null;
  resuelto_por: string | null;
  fecha_resolucion: Date | null;
  notas_resolucion: string | null;
  created_at: Date | null;
  updated_at: Date | null;
  n8n_workflow_id: string;
}
export interface WorkflowErroresInput {
  id?: string;
  workflow_id?: string | null;
  tipo_error: string;
  mensaje: string;
  detalles?: Json | null;
  stack_trace?: string | null;
  fecha_error?: Date | null;
  estado: string;
  notificado?: boolean | null;
  resuelto_por?: string | null;
  fecha_resolucion?: Date | null;
  notas_resolucion?: string | null;
  created_at?: Date | null;
  updated_at?: Date | null;
  n8n_workflow_id: string;
}
const workflow_errores = {
  tableName: 'workflow_errores',
  columns: ['id', 'workflow_id', 'tipo_error', 'mensaje', 'detalles', 'stack_trace', 'fecha_error', 'estado', 'notificado', 'resuelto_por', 'fecha_resolucion', 'notas_resolucion', 'created_at', 'updated_at', 'n8n_workflow_id'],
  requiredForInsert: ['tipo_error', 'mensaje', 'estado', 'n8n_workflow_id'],
  primaryKey: 'id',
  foreignKeys: {
    workflow_id: { table: 'workflows', column: 'id', $type: null as unknown as Workflows },
    resuelto_por: { table: 'usuarios', column: 'id', $type: null as unknown as Usuarios },
  },
  $type: null as unknown as WorkflowErrores,
  $input: null as unknown as WorkflowErroresInput
} as const;

// Table workflows
export interface Workflows {
  id: string;
  n8n_workflow_id: string | null;
  nombre: string;
  descripcion: string | null;
  proyecto_id: string | null;
  json_configuracion: Json | null;
  created_at: Date | null;
  updated_at: Date | null;
  etiquetas: Json | null;
  system_prompt_plantilla: string | null;
  user_prompt_plantilla: string | null;
  system_prompt_mejorado: string | null;
  user_prompt_mejorado: string | null;
}
export interface WorkflowsInput {
  id?: string;
  n8n_workflow_id?: string | null;
  nombre: string;
  descripcion?: string | null;
  proyecto_id?: string | null;
  json_configuracion?: Json | null;
  created_at?: Date | null;
  updated_at?: Date | null;
  etiquetas?: Json | null;
  system_prompt_plantilla?: string | null;
  user_prompt_plantilla?: string | null;
  system_prompt_mejorado?: string | null;
  user_prompt_mejorado?: string | null;
}
const workflows = {
  tableName: 'workflows',
  columns: ['id', 'n8n_workflow_id', 'nombre', 'descripcion', 'proyecto_id', 'json_configuracion', 'created_at', 'updated_at', 'etiquetas', 'system_prompt_plantilla', 'user_prompt_plantilla', 'system_prompt_mejorado', 'user_prompt_mejorado'],
  requiredForInsert: ['nombre'],
  primaryKey: 'id',
  foreignKeys: { proyecto_id: { table: 'proyectos', column: 'id', $type: null as unknown as Proyectos }, },
  $type: null as unknown as Workflows,
  $input: null as unknown as WorkflowsInput
} as const;


export interface TableTypes {
  agentes: {
    select: Agentes;
    input: AgentesInput;
  };
  agentes_companeros: {
    select: AgentesCompaneros;
    input: AgentesCompanerosInput;
  };
  agentes_sops_y_guias: {
    select: AgentesSopsYGuias;
    input: AgentesSopsYGuiasInput;
  };
  agentes_tareas: {
    select: AgentesTareas;
    input: AgentesTareasInput;
  };
  agentes_tools: {
    select: AgentesTools;
    input: AgentesToolsInput;
  };
  chat_performance_metrics: {
    select: ChatPerformanceMetrics;
    input: ChatPerformanceMetricsInput;
  };
  comunicaciones: {
    select: Comunicaciones;
    input: ComunicacionesInput;
  };
  configuracion: {
    select: Configuracion;
    input: ConfiguracionInput;
  };
  contratos: {
    select: Contratos;
    input: ContratosInput;
  };
  departamentos: {
    select: Departamentos;
    input: DepartamentosInput;
  };
  diagnosticos: {
    select: Diagnosticos;
    input: DiagnosticosInput;
  };
  doc_exports: {
    select: DocExports;
    input: DocExportsInput;
  };
  documentos_empresa: {
    select: DocumentosEmpresa;
    input: DocumentosEmpresaInput;
  };
  empresas: {
    select: Empresas;
    input: EmpresasInput;
  };
  etiquetas: {
    select: Etiquetas;
    input: EtiquetasInput;
  };
  evaluaciones: {
    select: Evaluaciones;
    input: EvaluacionesInput;
  };
  facturas: {
    select: Facturas;
    input: FacturasInput;
  };
  grabaciones: {
    select: Grabaciones;
    input: GrabacionesInput;
  };
  hallazgos_clientes: {
    select: HallazgosClientes;
    input: HallazgosClientesInput;
  };
  hallazgos_reuniones_departamentos: {
    select: HallazgosReunionesDepartamentos;
    input: HallazgosReunionesDepartamentosInput;
  };
  human_intervention_requests: {
    select: HumanInterventionRequests;
    input: HumanInterventionRequestsInput;
  };
  ideas: {
    select: Ideas;
    input: IdeasInput;
  };
  llm_models: {
    select: LlmModels;
    input: LlmModelsInput;
  };
  llm_providers: {
    select: LlmProviders;
    input: LlmProvidersInput;
  };
  logs_sistema: {
    select: LogsSistema;
    input: LogsSistemaInput;
  };
  mejoras_agentes: {
    select: MejorasAgentes;
    input: MejorasAgentesInput;
  };
  notificaciones: {
    select: Notificaciones;
    input: NotificacionesInput;
  };
  oportunidades: {
    select: Oportunidades;
    input: OportunidadesInput;
  };
  personas: {
    select: Personas;
    input: PersonasInput;
  };
  plantillas_tareas: {
    select: PlantillasTareas;
    input: PlantillasTareasInput;
  };
  preguntas: {
    select: Preguntas;
    input: PreguntasInput;
  };
  procesos: {
    select: Procesos;
    input: ProcesosInput;
  };
  procesos_clientes: {
    select: ProcesosClientes;
    input: ProcesosClientesInput;
  };
  procesos_clientes_departamentos: {
    select: ProcesosClientesDepartamentos;
    input: ProcesosClientesDepartamentosInput;
  };
  procesos_clientes_responsables: {
    select: ProcesosClientesResponsables;
    input: ProcesosClientesResponsablesInput;
  };
  procesos_fuentes_informacion: {
    select: ProcesosFuentesInformacion;
    input: ProcesosFuentesInformacionInput;
  };
  procesos_plantillas: {
    select: ProcesosPlantillas;
    input: ProcesosPlantillasInput;
  };
  procesos_tareas_plantilla: {
    select: ProcesosTareasPlantilla;
    input: ProcesosTareasPlantillaInput;
  };
  proyecto_personas: {
    select: ProyectoPersonas;
    input: ProyectoPersonasInput;
  };
  proyectos: {
    select: Proyectos;
    input: ProyectosInput;
  };
  proyectos_empresas: {
    select: ProyectosEmpresas;
    input: ProyectosEmpresasInput;
  };
  proyectos_procesos: {
    select: ProyectosProcesos;
    input: ProyectosProcesosInput;
  };
  retos_subtareas: {
    select: RetosSubtareas;
    input: RetosSubtareasInput;
  };
  retos_usuarios: {
    select: RetosUsuarios;
    input: RetosUsuariosInput;
  };
  reunion_empresas_asociadas: {
    select: ReunionEmpresasAsociadas;
    input: ReunionEmpresasAsociadasInput;
  };
  reunion_personas_asociadas: {
    select: ReunionPersonasAsociadas;
    input: ReunionPersonasAsociadasInput;
  };
  reunion_speaker_asignaciones: {
    select: ReunionSpeakerAsignaciones;
    input: ReunionSpeakerAsignacionesInput;
  };
  reuniones: {
    select: Reuniones;
    input: ReunionesInput;
  };
  sops_y_guias: {
    select: SopsYGuias;
    input: SopsYGuiasInput;
  };
  tareas: {
    select: Tareas;
    input: TareasInput;
  };
  tareas_clientes: {
    select: TareasClientes;
    input: TareasClientesInput;
  };
  tareas_clientes_responsables: {
    select: TareasClientesResponsables;
    input: TareasClientesResponsablesInput;
  };
  tareas_empresas: {
    select: TareasEmpresas;
    input: TareasEmpresasInput;
  };
  tareas_etiquetas: {
    select: TareasEtiquetas;
    input: TareasEtiquetasInput;
  };
  thread_message_counts: {
    select: ThreadMessageCounts;
    input: ThreadMessageCountsInput;
  };
  threads: {
    select: Threads;
    input: ThreadsInput;
  };
  threads_metadata: {
    select: ThreadsMetadata;
    input: ThreadsMetadataInput;
  };
  threads_resumen: {
    select: ThreadsResumen;
    input: ThreadsResumenInput;
  };
  ticket_mensajes: {
    select: TicketMensajes;
    input: TicketMensajesInput;
  };
  tickets: {
    select: Tickets;
    input: TicketsInput;
  };
  tools: {
    select: Tools;
    input: ToolsInput;
  };
  usuarios: {
    select: Usuarios;
    input: UsuariosInput;
  };
  workflow_errores: {
    select: WorkflowErrores;
    input: WorkflowErroresInput;
  };
  workflows: {
    select: Workflows;
    input: WorkflowsInput;
  };
}

export const tables = {
  agentes,
  agentes_companeros,
  agentes_sops_y_guias,
  agentes_tareas,
  agentes_tools,
  chat_performance_metrics,
  comunicaciones,
  configuracion,
  contratos,
  departamentos,
  diagnosticos,
  doc_exports,
  documentos_empresa,
  empresas,
  etiquetas,
  evaluaciones,
  facturas,
  grabaciones,
  hallazgos_clientes,
  hallazgos_reuniones_departamentos,
  human_intervention_requests,
  ideas,
  llm_models,
  llm_providers,
  logs_sistema,
  mejoras_agentes,
  notificaciones,
  oportunidades,
  personas,
  plantillas_tareas,
  preguntas,
  procesos,
  procesos_clientes,
  procesos_clientes_departamentos,
  procesos_clientes_responsables,
  procesos_fuentes_informacion,
  procesos_plantillas,
  procesos_tareas_plantilla,
  proyecto_personas,
  proyectos,
  proyectos_empresas,
  proyectos_procesos,
  retos_subtareas,
  retos_usuarios,
  reunion_empresas_asociadas,
  reunion_personas_asociadas,
  reunion_speaker_asignaciones,
  reuniones,
  sops_y_guias,
  tareas,
  tareas_clientes,
  tareas_clientes_responsables,
  tareas_empresas,
  tareas_etiquetas,
  thread_message_counts,
  threads,
  threads_metadata,
  threads_resumen,
  ticket_mensajes,
  tickets,
  tools,
  usuarios,
  workflow_errores,
  workflows,
}
