export type Json = unknown;
export type enum_ideas_estado = 'descartada' | 'implementada' | 'pendiente';
export type enum_ideas_prioridad = 'Alta' | 'Baja' | 'Media' | 'Urgente';
export type estado_contrato = 'activo' | 'borrador' | 'cancelado' | 'en_pausa' | 'finalizado' | 'firmado' | 'pendiente_firma' | 'renovado';
export type estado_evaluacion_enum = 'descartado' | 'pendiente' | 'revisado';
export type estado_factura = 'anulada' | 'borrador' | 'cancelada' | 'emitida' | 'enviada' | 'pagada' | 'vencida';
export type estado_hallazgo_enum = 'identificado' | 'pendiente_revision_humana';
export type estado_mejora = 'aplicado' | 'aprobado_para_aplicar' | 'descartado' | 'pendiente_revision_humana';
export type estado_mejora_agente_enum = 'aplicado' | 'aprobado' | 'descartado' | 'pendiente';
export type estado_proceso_cliente_enum = 'definicion_completa' | 'definicion_parcial' | 'identificado' | 'pendiente_revision_humana';
export type estado_tarea = 'Bloqueada' | 'Completada' | 'En Progreso' | 'En Revisión' | 'Pendiente';
export type evaluaciones_estado_enum = 'mejoras_aplicadas' | 'pendiente_mejoras' | 'pendiente_revision' | 'revisado';
export type grabacion_entidad_tipo = 'pregunta_cliente' | 'proceso_cliente' | 'tarea_cliente';
export type grabacion_estado_procesamiento = 'completado' | 'en_procesamiento' | 'error' | 'pendiente';
export type grabacion_tipo = 'solo_audio' | 'video_pantalla';
export type intervention_status = 'cancelado' | 'fallido' | 'pendiente' | 'resuelto';
export type pregunta_estado = 'en_procesamiento_ia' | 'pendiente_respuesta' | 'procesada' | 'respondida';
export type pregunta_visibilidad = 'interno_aceleralia' | 'portal_cliente';
export type prioridad_general_enum = 'Alta' | 'Baja' | 'Media' | 'Urgente';
export type prioridad_tarea = 'Alta' | 'Baja' | 'Media' | 'Urgente';
export type procesos_fuentes_informacion_estado = 'obsoleto' | 'pendiente_revision' | 'valido';
export type reto_estado = 'completado' | 'expirado' | 'pendiente';
export type reto_tipo_accion = 'definir_duracion_proceso' | 'definir_proceso' | 'definir_tarea' | 'responder_pregunta';
export type rol_decision_lead_enum = 'Influenciador' | 'Otro' | 'Tomador de decision';
export type sop_guia_type_enum = 'Guia' | 'SOP';
export type task_status = 'cancelled' | 'completed' | 'failed' | 'interrupted' | 'pending' | 'ready' | 'running';
export type thread_request_type_enum = 'API' | 'chat';
export type tipo_complejidad_automatizacion = 'alta' | 'baja' | 'media' | 'muy_alta' | 'pendiente_de_analisis';
export type tipo_frecuencia_periodo = 'anual' | 'diario' | 'indefinido' | 'mensual' | 'semanal' | 'trimestral';
export type tipo_hallazgo_enum = 'deficit_gobernanza_datos' | 'equipamiento_inadecuado' | 'falta_estandarizacion' | 'ineficiencia' | 'ladron_tiempo' | 'oportunidad_mejora' | 'riesgo_identificado';
export type tipo_prioridad_automatizacion = 'alta' | 'baja' | 'critica' | 'media' | 'pendiente_de_analisis';
export type tipo_proceso_enum = 'Externo' | 'Interno';
export type tipo_relacion_empresa_enum = 'Cliente' | 'Colaborador' | 'Lead' | 'Otro';
export type urgencia_tarea_enum = 'No Urgente' | 'Urgente';
export interface Agentes {
    id: string;
    nombre: string;
    descripcion: string | null;
    system_prompt: string;
    activo: boolean | null;
    created_at: Date | null;
    updated_at: Date | null;
    selected_llm_model_id: string | null;
    db_schema: boolean | null;
    temperature: number | null;
}
export interface AgentesInput {
    id?: string;
    nombre: string;
    descripcion?: string | null;
    system_prompt: string;
    activo?: boolean | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    selected_llm_model_id?: string | null;
    db_schema?: boolean | null;
    temperature?: number | null;
}
export interface AgentesCompaneros {
    agente_id: string;
    companero_id: string;
    created_at: Date | null;
    id: string;
}
export interface AgentesCompanerosInput {
    agente_id: string;
    companero_id: string;
    created_at?: Date | null;
    id?: string;
}
export interface AgentesSopsYGuias {
    id: string;
    agente_id: string;
    sop_guia_id: string;
    created_at: Date | null;
}
export interface AgentesSopsYGuiasInput {
    id?: string;
    agente_id: string;
    sop_guia_id: string;
    created_at?: Date | null;
}
export interface AgentesTareas {
    id: string;
    agente_id: string;
    descripcion: string;
    input_data: Json | null;
    estado: task_status;
    resultado: Json | null;
    prioridad: number | null;
    tarea_origen_id: string | null;
    tarea_origen_tipo: string | null;
    creado_por_tipo: string;
    creado_por_id: string;
    iniciado_en: Date | null;
    finalizado_en: Date | null;
    created_at: Date;
    updated_at: Date;
    langgraph_thread_id: string | null;
    titulo: string;
    error_mensaje: string | null;
    error_detalles: Json | null;
}
export interface AgentesTareasInput {
    id?: string;
    agente_id: string;
    descripcion: string;
    input_data?: Json | null;
    estado?: task_status;
    resultado?: Json | null;
    prioridad?: number | null;
    tarea_origen_id?: string | null;
    tarea_origen_tipo?: string | null;
    creado_por_tipo: string;
    creado_por_id: string;
    iniciado_en?: Date | null;
    finalizado_en?: Date | null;
    created_at?: Date;
    updated_at?: Date;
    langgraph_thread_id?: string | null;
    titulo: string;
    error_mensaje?: string | null;
    error_detalles?: Json | null;
}
export interface AgentesTools {
    agente_id: string;
    tool_id: string;
    created_at: Date | null;
    id: string;
}
export interface AgentesToolsInput {
    agente_id: string;
    tool_id: string;
    created_at?: Date | null;
    id?: string;
}
export interface ChatPerformanceMetrics {
    thread_id: number | null;
    total_messages: number | null;
    user_messages: number | null;
    agent_answers: number | null;
    intermediate_steps: number | null;
    last_activity: Date | null;
    first_activity: Date | null;
    conversation_duration_hours: number | null;
    avg_message_length: number | null;
    total_content_length: number | null;
}
export interface ChatPerformanceMetricsInput {
    thread_id?: number | null;
    total_messages?: number | null;
    user_messages?: number | null;
    agent_answers?: number | null;
    intermediate_steps?: number | null;
    last_activity?: Date | null;
    first_activity?: Date | null;
    conversation_duration_hours?: number | null;
    avg_message_length?: number | null;
    total_content_length?: number | null;
}
export interface Comunicaciones {
    id: string;
    tipo: string;
    fecha_hora: Date | null;
    direccion: string | null;
    asunto: string | null;
    contenido: string | null;
    transcripcion_url: string | null;
    duracion_minutos: number | null;
    participante_usuario_id: string | null;
    participante_persona_id: string | null;
    participante_externo_detalle: string | null;
    relacionado_oportunidad_id: string | null;
    relacionado_empresa_id: string | null;
    relacionado_proyecto_id: string | null;
    relacionado_tarea_id: string | null;
    fuente_sistema: string | null;
    created_at: Date | null;
}
export interface ComunicacionesInput {
    id?: string;
    tipo: string;
    fecha_hora?: Date | null;
    direccion?: string | null;
    asunto?: string | null;
    contenido?: string | null;
    transcripcion_url?: string | null;
    duracion_minutos?: number | null;
    participante_usuario_id?: string | null;
    participante_persona_id?: string | null;
    participante_externo_detalle?: string | null;
    relacionado_oportunidad_id?: string | null;
    relacionado_empresa_id?: string | null;
    relacionado_proyecto_id?: string | null;
    relacionado_tarea_id?: string | null;
    fuente_sistema?: string | null;
    created_at?: Date | null;
}
export interface Configuracion {
    id: string;
    clave: string;
    valor: string;
    descripcion: string | null;
    created_at: Date;
    updated_at: Date;
}
export interface ConfiguracionInput {
    id?: string;
    clave: string;
    valor: string;
    descripcion?: string | null;
    created_at?: Date;
    updated_at?: Date;
}
export interface Contratos {
    id: string;
    codigo: string;
    titulo: string;
    empresa_id: string;
    proyecto_id: string | null;
    fecha_inicio: Date;
    fecha_fin: Date | null;
    renovacion_automatica: boolean | null;
    periodo_renovacion: string | null;
    valor_contrato: number | null;
    moneda: string | null;
    periodicidad_facturacion: string | null;
    dia_facturacion: number | null;
    estado: estado_contrato;
    fecha_firma: Date | null;
    firmado_cliente: boolean | null;
    firmado_empresa: boolean | null;
    url_documento: string | null;
    persona_firma_id: string | null;
    terminos_condiciones: string | null;
    clausulas: Json | null;
    fecha_cancelacion: Date | null;
    motivo_cancelacion: string | null;
    contrato_relacionado_id: string | null;
    info_adicional: string | null;
    creado_por: string | null;
    created_at: Date | null;
    updated_at: Date | null;
}
export interface ContratosInput {
    id?: string;
    codigo: string;
    titulo: string;
    empresa_id: string;
    proyecto_id?: string | null;
    fecha_inicio: Date;
    fecha_fin?: Date | null;
    renovacion_automatica?: boolean | null;
    periodo_renovacion?: string | null;
    valor_contrato?: number | null;
    moneda?: string | null;
    periodicidad_facturacion?: string | null;
    dia_facturacion?: number | null;
    estado?: estado_contrato;
    fecha_firma?: Date | null;
    firmado_cliente?: boolean | null;
    firmado_empresa?: boolean | null;
    url_documento?: string | null;
    persona_firma_id?: string | null;
    terminos_condiciones?: string | null;
    clausulas?: Json | null;
    fecha_cancelacion?: Date | null;
    motivo_cancelacion?: string | null;
    contrato_relacionado_id?: string | null;
    info_adicional?: string | null;
    creado_por?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
}
export interface Departamentos {
    id: string;
    empresa_id: string;
    nombre: string;
    descripcion: string | null;
    created_at: Date | null;
    updated_at: Date | null;
    info_adicional: string | null;
}
export interface DepartamentosInput {
    id?: string;
    empresa_id: string;
    nombre: string;
    descripcion?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    info_adicional?: string | null;
}
export interface Diagnosticos {
    id: string;
    empresa_id: string | null;
    titulo: string | null;
    fecha_generacion: Date | null;
    diagrama_reactflow: string | null;
    plan_aceleracion_html: string | null;
    estado: string | null;
    version: number | null;
    creado_por: string | null;
    created_at: Date | null;
    updated_at: Date | null;
    diagrama_proceso_json: Json | null;
}
export interface DiagnosticosInput {
    id?: string;
    empresa_id?: string | null;
    titulo?: string | null;
    fecha_generacion?: Date | null;
    diagrama_reactflow?: string | null;
    plan_aceleracion_html?: string | null;
    estado?: string | null;
    version?: number | null;
    creado_por?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    diagrama_proceso_json?: Json | null;
}
export interface DocExports {
    id: string;
    threads_row_id: string;
    user_id: string;
    status: string;
    doc_url: string | null;
    created_at: Date;
    updated_at: Date;
}
export interface DocExportsInput {
    id?: string;
    threads_row_id: string;
    user_id: string;
    status: string;
    doc_url?: string | null;
    created_at?: Date;
    updated_at?: Date;
}
export interface DocumentosEmpresa {
    id: string;
    nombre_documento: string | null;
    url_documento: string | null;
    ruta_carpeta_drive: string | null;
    resumen: string | null;
    usuario_documento_id: string | null;
    creado_por_agente_id: string | null;
    empresa_relacionada_id: string | null;
    proyecto_relacionado_id: string | null;
    proceso_relacionado_id: string | null;
    info_adicional: string | null;
    created_at: Date | null;
    updated_at: Date | null;
}
export interface DocumentosEmpresaInput {
    id?: string;
    nombre_documento?: string | null;
    url_documento?: string | null;
    ruta_carpeta_drive?: string | null;
    resumen?: string | null;
    usuario_documento_id?: string | null;
    creado_por_agente_id?: string | null;
    empresa_relacionada_id?: string | null;
    proyecto_relacionado_id?: string | null;
    proceso_relacionado_id?: string | null;
    info_adicional?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
}
export interface Empresas {
    id: string;
    nombre: string;
    sector: string | null;
    logo_url: string | null;
    direccion: string | null;
    telefono: string | null;
    email_principal: string | null;
    website: string | null;
    fecha_alta: Date | null;
    activo: boolean | null;
    created_at: Date | null;
    updated_at: Date | null;
    info_adicional: string | null;
    nif_cif: string | null;
    direccion_fiscal: string | null;
    tipo_empresa: string | null;
    descripcion: string | null;
    tipo_relacion: tipo_relacion_empresa_enum | null;
}
export interface EmpresasInput {
    id?: string;
    nombre: string;
    sector?: string | null;
    logo_url?: string | null;
    direccion?: string | null;
    telefono?: string | null;
    email_principal?: string | null;
    website?: string | null;
    fecha_alta?: Date | null;
    activo?: boolean | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    info_adicional?: string | null;
    nif_cif?: string | null;
    direccion_fiscal?: string | null;
    tipo_empresa?: string | null;
    descripcion?: string | null;
    tipo_relacion?: tipo_relacion_empresa_enum | null;
}
export interface Etiquetas {
    id: string;
    nombre: string;
    color: string | null;
    descripcion: string | null;
    created_at: Date | null;
    updated_at: Date | null;
}
export interface EtiquetasInput {
    id?: string;
    nombre: string;
    color?: string | null;
    descripcion?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
}
export interface Evaluaciones {
    id: number;
    created_at: Date;
    nombre_agente_workflow: string | null;
    proposito_agente: string | null;
    criterios_adicionales_evaluacion: string | null;
    agente_output: string | null;
    puntuacion: number | null;
    argumentos_puntuacion: string | null;
    sugerencias_mejora: string | null;
    execution_id: string | null;
    n8n_workflow_id: string | null;
    estado: evaluaciones_estado_enum | null;
    agente_id: string | null;
    nombre_agente_amigable: string | null;
    mejora_agente_id: string | null;
    user_id: string | null;
}
export interface EvaluacionesInput {
    id: number;
    created_at?: Date;
    nombre_agente_workflow?: string | null;
    proposito_agente?: string | null;
    criterios_adicionales_evaluacion?: string | null;
    agente_output?: string | null;
    puntuacion?: number | null;
    argumentos_puntuacion?: string | null;
    sugerencias_mejora?: string | null;
    execution_id?: string | null;
    n8n_workflow_id?: string | null;
    estado?: evaluaciones_estado_enum | null;
    agente_id?: string | null;
    nombre_agente_amigable?: string | null;
    mejora_agente_id?: string | null;
    user_id?: string | null;
}
export interface Facturas {
    id: string;
    numero_factura: string;
    serie: string | null;
    empresa_id: string;
    proyecto_id: string | null;
    contrato_id: string | null;
    fecha_emision: Date;
    fecha_vencimiento: Date;
    subtotal: number;
    iva: number;
    total: number;
    estado: estado_factura;
    fecha_pago: Date | null;
    metodo_pago: string | null;
    referencia_pago: string | null;
    pdf_url: string | null;
    xml_url: string | null;
    plantilla_id: string | null;
    emisor_nombre: string;
    emisor_nif: string;
    emisor_direccion: string;
    receptor_nombre: string;
    receptor_nif: string;
    receptor_direccion: string;
    items: Json;
    recordatorios_enviados: number | null;
    fecha_ultimo_recordatorio: Date | null;
    info_adicional: string | null;
    creado_por: string | null;
    created_at: Date | null;
    updated_at: Date | null;
}
export interface FacturasInput {
    id?: string;
    numero_factura: string;
    serie?: string | null;
    empresa_id: string;
    proyecto_id?: string | null;
    contrato_id?: string | null;
    fecha_emision?: Date;
    fecha_vencimiento: Date;
    subtotal: number;
    iva: number;
    total: number;
    estado?: estado_factura;
    fecha_pago?: Date | null;
    metodo_pago?: string | null;
    referencia_pago?: string | null;
    pdf_url?: string | null;
    xml_url?: string | null;
    plantilla_id?: string | null;
    emisor_nombre: string;
    emisor_nif: string;
    emisor_direccion: string;
    receptor_nombre: string;
    receptor_nif: string;
    receptor_direccion: string;
    items?: Json;
    recordatorios_enviados?: number | null;
    fecha_ultimo_recordatorio?: Date | null;
    info_adicional?: string | null;
    creado_por?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
}
export interface Grabaciones {
    id: string;
    entidad_relacionada_id: string;
    entidad_relacionada_tipo: grabacion_entidad_tipo;
    usuario_id: string;
    url_almacenamiento: string;
    estado_procesamiento: grabacion_estado_procesamiento;
    tipo_grabacion: grabacion_tipo;
    transcripcion: string | null;
    duracion_segundos: number | null;
    info_adicional: string | null;
    created_at: Date;
    updated_at: Date;
}
export interface GrabacionesInput {
    id?: string;
    entidad_relacionada_id: string;
    entidad_relacionada_tipo: grabacion_entidad_tipo;
    usuario_id: string;
    url_almacenamiento: string;
    estado_procesamiento?: grabacion_estado_procesamiento;
    tipo_grabacion: grabacion_tipo;
    transcripcion?: string | null;
    duracion_segundos?: number | null;
    info_adicional?: string | null;
    created_at?: Date;
    updated_at?: Date;
}
export interface HallazgosClientes {
    id: string;
    reunion_id: string;
    empresa_id: string;
    persona_id: string | null;
    tipo: tipo_hallazgo_enum | null;
    descripcion: string | null;
    impacto: string | null;
    posible_solucion: string | null;
    estado: estado_hallazgo_enum | null;
    created_at: Date | null;
    updated_at: Date | null;
    procesos_relacionados: Json | null;
    titulo: string;
}
export interface HallazgosClientesInput {
    id?: string;
    reunion_id: string;
    empresa_id: string;
    persona_id?: string | null;
    tipo?: tipo_hallazgo_enum | null;
    descripcion?: string | null;
    impacto?: string | null;
    posible_solucion?: string | null;
    estado?: estado_hallazgo_enum | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    procesos_relacionados?: Json | null;
    titulo: string;
}
export interface HallazgosReunionesDepartamentos {
    hallazgo_reunion_id: string;
    departamento_id: string;
    created_at: Date;
}
export interface HallazgosReunionesDepartamentosInput {
    hallazgo_reunion_id: string;
    departamento_id: string;
    created_at?: Date;
}
export interface HumanInterventionRequests {
    id: string;
    langgraph_thread_id: string;
    asistente_id: string;
    request_type: string;
    request_details: Json | null;
    status: string;
    resolution_data: Json | null;
    requested_at: Date;
    resolved_at: Date | null;
    resolved_by: string | null;
    created_at: Date;
    updated_at: Date;
    triggering_tool_call_id: string | null;
}
export interface HumanInterventionRequestsInput {
    id?: string;
    langgraph_thread_id: string;
    asistente_id: string;
    request_type: string;
    request_details?: Json | null;
    status?: string;
    resolution_data?: Json | null;
    requested_at?: Date;
    resolved_at?: Date | null;
    resolved_by?: string | null;
    created_at?: Date;
    updated_at?: Date;
    triggering_tool_call_id?: string | null;
}
export interface Ideas {
    id: string;
    titulo: string;
    descripcion: string | null;
    empresa_relacionada_id: string | null;
    proyecto_relacionado_id: string | null;
    created_at: Date | null;
    updated_at: Date | null;
    estado: enum_ideas_estado | null;
    prioridad: enum_ideas_prioridad | null;
}
export interface IdeasInput {
    id?: string;
    titulo: string;
    descripcion?: string | null;
    empresa_relacionada_id?: string | null;
    proyecto_relacionado_id?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    estado?: enum_ideas_estado | null;
    prioridad?: enum_ideas_prioridad | null;
}
export interface LlmModels {
    id: string;
    provider_id: string;
    model_name: string;
    description: string | null;
    context_window: number | null;
    active: boolean;
    created_at: Date;
    input_cost_per_million_tokens: number | null;
    output_cost_per_million_tokens: number | null;
}
export interface LlmModelsInput {
    id?: string;
    provider_id: string;
    model_name: string;
    description?: string | null;
    context_window?: number | null;
    active?: boolean;
    created_at?: Date;
    input_cost_per_million_tokens?: number | null;
    output_cost_per_million_tokens?: number | null;
}
export interface LlmProviders {
    id: string;
    provider_key: string;
    provider_name: string;
    created_at: Date;
}
export interface LlmProvidersInput {
    id?: string;
    provider_key: string;
    provider_name: string;
    created_at?: Date;
}
export interface LogsSistema {
    id: string;
    tipo: string;
    origen: string;
    mensaje: string;
    detalles: Json | null;
    usuario_id: string | null;
    timestamp: Date | null;
}
export interface LogsSistemaInput {
    id?: string;
    tipo: string;
    origen: string;
    mensaje: string;
    detalles?: Json | null;
    usuario_id?: string | null;
    timestamp?: Date | null;
}
export interface MejorasAgentes {
    id: string;
    agente_id: string | null;
    n8n_workflow_id: string | null;
    explicacion_mejoras: string;
    estado: estado_mejora;
    created_at: Date;
    updated_at: Date | null;
    system_prompt_original: string | null;
    user_prompt_original: string | null;
    system_prompt_mejorado: string | null;
    user_prompt_mejorado: string | null;
    set_node_id: string | null;
    nombre_agente_amigable: string | null;
    nombre_workflow: string | null;
    user_id: string;
}
export interface MejorasAgentesInput {
    id?: string;
    agente_id?: string | null;
    n8n_workflow_id?: string | null;
    explicacion_mejoras: string;
    estado?: estado_mejora;
    created_at?: Date;
    updated_at?: Date | null;
    system_prompt_original?: string | null;
    user_prompt_original?: string | null;
    system_prompt_mejorado?: string | null;
    user_prompt_mejorado?: string | null;
    set_node_id?: string | null;
    nombre_agente_amigable?: string | null;
    nombre_workflow?: string | null;
    user_id: string;
}
export interface Notificaciones {
    id: string;
    usuario_id: string;
    titulo: string;
    mensaje: string;
    url_destino: string | null;
    estado: string;
    tipo_notificacion: string;
    created_at: Date;
}
export interface NotificacionesInput {
    id?: string;
    usuario_id: string;
    titulo: string;
    mensaje: string;
    url_destino?: string | null;
    estado?: string;
    tipo_notificacion: string;
    created_at?: Date;
}
export interface Oportunidades {
    id: string;
    titulo: string;
    descripcion: string | null;
    empresa_id: string | null;
    persona_contacto_id: string | null;
    valor_estimado: number | null;
    moneda: string | null;
    etapa: string | null;
    probabilidad_cierre: number | null;
    fecha_cierre_estimada: Date | null;
    fecha_cierre_real: Date | null;
    motivo_perdida: string | null;
    documento_url: string | null;
    asignado_usuario_id: string | null;
    created_at: Date | null;
    updated_at: Date | null;
}
export interface OportunidadesInput {
    id?: string;
    titulo: string;
    descripcion?: string | null;
    empresa_id?: string | null;
    persona_contacto_id?: string | null;
    valor_estimado?: number | null;
    moneda?: string | null;
    etapa?: string | null;
    probabilidad_cierre?: number | null;
    fecha_cierre_estimada?: Date | null;
    fecha_cierre_real?: Date | null;
    motivo_perdida?: string | null;
    documento_url?: string | null;
    asignado_usuario_id?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
}
export interface Personas {
    id: string;
    empresa_id: string | null;
    departamento_id: string | null;
    nombre: string;
    apellidos: string | null;
    email: string | null;
    telefono: string | null;
    cargo: string | null;
    tipo: string[];
    es_decision_maker: boolean | null;
    entrevistado: boolean | null;
    fecha_entrevista: Date | null;
    fecha_alta: Date | null;
    fecha_baja: Date | null;
    activo: boolean | null;
    info_adicional: string | null;
    created_at: Date | null;
    updated_at: Date | null;
    responsable_departamento: boolean | null;
    linkedin_url: string | null;
    usuario_id: string | null;
}
export interface PersonasInput {
    id?: string;
    empresa_id?: string | null;
    departamento_id?: string | null;
    nombre: string;
    apellidos?: string | null;
    email?: string | null;
    telefono?: string | null;
    cargo?: string | null;
    tipo: string[];
    es_decision_maker?: boolean | null;
    entrevistado?: boolean | null;
    fecha_entrevista?: Date | null;
    fecha_alta?: Date | null;
    fecha_baja?: Date | null;
    activo?: boolean | null;
    info_adicional?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    responsable_departamento?: boolean | null;
    linkedin_url?: string | null;
    usuario_id?: string | null;
}
export interface PlantillasTareas {
    id: string;
    titulo_plantilla: string;
    descripcion_base: string | null;
    duracion_estimada_horas: number | null;
    prioridad_predeterminada: prioridad_general_enum | null;
    info_adicional_plantilla: string | null;
    created_at: Date;
    updated_at: Date;
}
export interface PlantillasTareasInput {
    id?: string;
    titulo_plantilla: string;
    descripcion_base?: string | null;
    duracion_estimada_horas?: number | null;
    prioridad_predeterminada?: prioridad_general_enum | null;
    info_adicional_plantilla?: string | null;
    created_at?: Date;
    updated_at?: Date;
}
export interface Preguntas {
    id: string;
    titulo: string;
    estado: pregunta_estado;
    visibilidad: pregunta_visibilidad;
    destinatario_usuario_id: string | null;
    origen_reunion_id: string | null;
    origen_proceso_id: string | null;
    respuesta_texto: string | null;
    respuesta_grabacion_id: string | null;
    info_adicional: string | null;
    created_at: Date;
    updated_at: Date;
}
export interface PreguntasInput {
    id?: string;
    titulo: string;
    estado?: pregunta_estado;
    visibilidad?: pregunta_visibilidad;
    destinatario_usuario_id?: string | null;
    origen_reunion_id?: string | null;
    origen_proceso_id?: string | null;
    respuesta_texto?: string | null;
    respuesta_grabacion_id?: string | null;
    info_adicional?: string | null;
    created_at?: Date;
    updated_at?: Date;
}
export interface Procesos {
    id: string;
    empresa_id: string;
    departamento_id: string | null;
    nombre: string;
    descripcion: string | null;
    es_repetitivo: boolean | null;
    es_cuello_botella: boolean | null;
    es_manual: boolean | null;
    valor_negocio: string | null;
    complejidad_automatizacion: string | null;
    prioridad_automatizacion: string | null;
    tiempo_estimado_manual: number | null;
    frecuencia: string | null;
    created_at: Date | null;
    updated_at: Date | null;
    info_adicional: string | null;
    persona_id: string | null;
    herramientas_utilizadas: Json | null;
    tipo_proceso: tipo_proceso_enum | null;
    proceso_plantilla_origen_id: string | null;
    reunion_origen_id: string | null;
}
export interface ProcesosInput {
    id?: string;
    empresa_id: string;
    departamento_id?: string | null;
    nombre: string;
    descripcion?: string | null;
    es_repetitivo?: boolean | null;
    es_cuello_botella?: boolean | null;
    es_manual?: boolean | null;
    valor_negocio?: string | null;
    complejidad_automatizacion?: string | null;
    prioridad_automatizacion?: string | null;
    tiempo_estimado_manual?: number | null;
    frecuencia?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    info_adicional?: string | null;
    persona_id?: string | null;
    herramientas_utilizadas?: Json | null;
    tipo_proceso?: tipo_proceso_enum | null;
    proceso_plantilla_origen_id?: string | null;
    reunion_origen_id?: string | null;
}
export interface ProcesosClientes {
    id: string;
    empresa_cliente_id: string;
    nombre: string;
    descripcion: string | null;
    estado_analisis: estado_proceso_cliente_enum | null;
    info_adicional: string | null;
    created_at: Date;
    updated_at: Date;
    reunion_origen_id: string | null;
    es_repetitivo: boolean | null;
    es_cuello_botella: boolean | null;
    es_manual: boolean | null;
    valor_negocio_cliente: string | null;
    complejidad_automatizacion_aceleralia: tipo_complejidad_automatizacion | null;
    prioridad_automatizacion_aceleralia: tipo_prioridad_automatizacion | null;
    duracion_minutos_por_ejecucion: number | null;
    frecuencia_periodo: tipo_frecuencia_periodo | null;
    herramientas_utilizadas_cliente: Json | null;
    frecuencia_ocurrencias: number | null;
}
export interface ProcesosClientesInput {
    id?: string;
    empresa_cliente_id: string;
    nombre: string;
    descripcion?: string | null;
    estado_analisis?: estado_proceso_cliente_enum | null;
    info_adicional?: string | null;
    created_at?: Date;
    updated_at?: Date;
    reunion_origen_id?: string | null;
    es_repetitivo?: boolean | null;
    es_cuello_botella?: boolean | null;
    es_manual?: boolean | null;
    valor_negocio_cliente?: string | null;
    complejidad_automatizacion_aceleralia?: tipo_complejidad_automatizacion | null;
    prioridad_automatizacion_aceleralia?: tipo_prioridad_automatizacion | null;
    duracion_minutos_por_ejecucion?: number | null;
    frecuencia_periodo?: tipo_frecuencia_periodo | null;
    herramientas_utilizadas_cliente?: Json | null;
    frecuencia_ocurrencias?: number | null;
}
export interface ProcesosClientesDepartamentos {
    proceso_cliente_id: string;
    departamento_id: string;
    created_at: Date;
}
export interface ProcesosClientesDepartamentosInput {
    proceso_cliente_id: string;
    departamento_id: string;
    created_at?: Date;
}
export interface ProcesosClientesResponsables {
    id: string;
    proceso_cliente_id: string;
    persona_cliente_id: string;
    created_at: Date;
    updated_at: Date;
}
export interface ProcesosClientesResponsablesInput {
    id?: string;
    proceso_cliente_id: string;
    persona_cliente_id: string;
    created_at?: Date;
    updated_at?: Date;
}
export interface ProcesosFuentesInformacion {
    id: string;
    proceso_cliente_id: string;
    nombre_informacion: string;
    descripcion: string | null;
    persona_id: string;
    formato: string;
    url_adjunto: string | null;
    estado: procesos_fuentes_informacion_estado;
    fecha_ultima_revision: Date | null;
    created_at: Date;
    updated_at: Date;
}
export interface ProcesosFuentesInformacionInput {
    id?: string;
    proceso_cliente_id: string;
    nombre_informacion: string;
    descripcion?: string | null;
    persona_id: string;
    formato: string;
    url_adjunto?: string | null;
    estado: procesos_fuentes_informacion_estado;
    fecha_ultima_revision?: Date | null;
    created_at?: Date;
    updated_at?: Date;
}
export interface ProcesosPlantillas {
    id: string;
    nombre_plantilla: string;
    descripcion_plantilla: string | null;
    objetivo_plantilla: string | null;
    info_adicional_plantilla: string | null;
    created_at: Date;
    updated_at: Date;
}
export interface ProcesosPlantillasInput {
    id?: string;
    nombre_plantilla: string;
    descripcion_plantilla?: string | null;
    objetivo_plantilla?: string | null;
    info_adicional_plantilla?: string | null;
    created_at?: Date;
    updated_at?: Date;
}
export interface ProcesosTareasPlantilla {
    id: string;
    proceso_plantilla_id: string;
    plantilla_tarea_id: string;
    orden_en_proceso: number;
    dias_desplazamiento: number | null;
    es_obligatoria: boolean | null;
    notas_especificas_proceso: string | null;
    created_at: Date;
    updated_at: Date;
}
export interface ProcesosTareasPlantillaInput {
    id?: string;
    proceso_plantilla_id: string;
    plantilla_tarea_id: string;
    orden_en_proceso: number;
    dias_desplazamiento?: number | null;
    es_obligatoria?: boolean | null;
    notas_especificas_proceso?: string | null;
    created_at?: Date;
    updated_at?: Date;
}
export interface ProyectoPersonas {
    id: string;
    proyecto_id: string | null;
    persona_id: string | null;
    rol: string | null;
    asignado_desde: Date | null;
    asignado_hasta: Date | null;
    porcentaje_dedicacion: number | null;
    es_responsable: boolean | null;
    info_adicional: string | null;
    created_at: Date | null;
    updated_at: Date | null;
}
export interface ProyectoPersonasInput {
    id?: string;
    proyecto_id?: string | null;
    persona_id?: string | null;
    rol?: string | null;
    asignado_desde?: Date | null;
    asignado_hasta?: Date | null;
    porcentaje_dedicacion?: number | null;
    es_responsable?: boolean | null;
    info_adicional?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
}
export interface Proyectos {
    id: string;
    nombre: string;
    descripcion: string | null;
    objetivo: string | null;
    estado: string;
    fecha_inicio: Date | null;
    fecha_fin_estimada: Date | null;
    fecha_fin_real: Date | null;
    presupuesto: number | null;
    created_at: Date | null;
    updated_at: Date | null;
    responsable_persona_id: string | null;
    info_adicional: string | null;
    prioridad: string | null;
    progreso: number | null;
    responsable_usuario_id: string | null;
}
export interface ProyectosInput {
    id?: string;
    nombre: string;
    descripcion?: string | null;
    objetivo?: string | null;
    estado: string;
    fecha_inicio?: Date | null;
    fecha_fin_estimada?: Date | null;
    fecha_fin_real?: Date | null;
    presupuesto?: number | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    responsable_persona_id?: string | null;
    info_adicional?: string | null;
    prioridad?: string | null;
    progreso?: number | null;
    responsable_usuario_id?: string | null;
}
export interface ProyectosEmpresas {
    proyecto_id: string;
    empresa_id: string;
}
export interface ProyectosEmpresasInput {
    proyecto_id: string;
    empresa_id: string;
}
export interface ProyectosProcesos {
    id: string;
    proyecto_id: string;
    proceso_id: string;
    created_at: Date;
}
export interface ProyectosProcesosInput {
    id?: string;
    proyecto_id: string;
    proceso_id: string;
    created_at?: Date;
}
export interface RetosSubtareas {
    id: string;
    reto_usuario_id: string;
    titulo: string;
    descripcion: string | null;
    tipo_accion: reto_tipo_accion;
    entidad_relacionada_id: string;
    entidad_relacionada_tipo: grabacion_entidad_tipo;
    estado: reto_estado;
    created_at: Date;
    updated_at: Date;
}
export interface RetosSubtareasInput {
    id?: string;
    reto_usuario_id: string;
    titulo: string;
    descripcion?: string | null;
    tipo_accion: reto_tipo_accion;
    entidad_relacionada_id: string;
    entidad_relacionada_tipo: grabacion_entidad_tipo;
    estado?: reto_estado;
    created_at?: Date;
    updated_at?: Date;
}
export interface RetosUsuarios {
    id: string;
    usuario_id: string;
    titulo: string;
    descripcion: string | null;
    puntos_recompensa: number;
    estado: reto_estado;
    prioridad: number | null;
    url_destino: string | null;
    created_at: Date;
    updated_at: Date;
}
export interface RetosUsuariosInput {
    id?: string;
    usuario_id: string;
    titulo: string;
    descripcion?: string | null;
    puntos_recompensa?: number;
    estado?: reto_estado;
    prioridad?: number | null;
    url_destino?: string | null;
    created_at?: Date;
    updated_at?: Date;
}
export interface ReunionEmpresasAsociadas {
    reunion_id: string;
    empresa_id: string;
    created_at: Date;
}
export interface ReunionEmpresasAsociadasInput {
    reunion_id: string;
    empresa_id: string;
    created_at?: Date;
}
export interface ReunionPersonasAsociadas {
    reunion_id: string;
    persona_id: string;
    created_at: Date;
}
export interface ReunionPersonasAsociadasInput {
    reunion_id: string;
    persona_id: string;
    created_at?: Date;
}
export interface ReunionSpeakerAsignaciones {
    id: string;
    reunion_id: string;
    speaker_tag: string;
    asignado_a_tipo: string;
    asignado_a_id: string;
    nombre_asignado: string | null;
    created_at: Date;
    updated_at: Date;
}
export interface ReunionSpeakerAsignacionesInput {
    id?: string;
    reunion_id: string;
    speaker_tag: string;
    asignado_a_tipo: string;
    asignado_a_id: string;
    nombre_asignado?: string | null;
    created_at?: Date;
    updated_at?: Date;
}
export interface Reuniones {
    id: string;
    user_id: string | null;
    titulo: string | null;
    observaciones_iniciales: string | null;
    url_grabacion_original: string | null;
    url_grabacion_publica: string | null;
    fecha_reunion: Date | null;
    transcripcion_raw: string | null;
    transcripcion_final: string | null;
    resumen: string | null;
    puntos_clave: Json | null;
    estado_procesamiento: string;
    info_adicional: string | null;
    created_at: Date;
    updated_at: Date;
    entrevista: boolean | null;
    video: boolean | null;
    duracion_minutos: number | null;
}
export interface ReunionesInput {
    id?: string;
    user_id?: string | null;
    titulo?: string | null;
    observaciones_iniciales?: string | null;
    url_grabacion_original?: string | null;
    url_grabacion_publica?: string | null;
    fecha_reunion?: Date | null;
    transcripcion_raw?: string | null;
    transcripcion_final?: string | null;
    resumen?: string | null;
    puntos_clave?: Json | null;
    estado_procesamiento?: string;
    info_adicional?: string | null;
    created_at?: Date;
    updated_at?: Date;
    entrevista?: boolean | null;
    video?: boolean | null;
    duracion_minutos?: number | null;
}
export interface SopsYGuias {
    id: string;
    titulo: string;
    descripcion: string | null;
    contenido_markdown: string;
    dueno_sop_id: string | null;
    created_at: Date | null;
    updated_at: Date | null;
    type: sop_guia_type_enum | null;
}
export interface SopsYGuiasInput {
    id?: string;
    titulo: string;
    descripcion?: string | null;
    contenido_markdown: string;
    dueno_sop_id?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    type?: sop_guia_type_enum | null;
}
export interface Tareas {
    id: string;
    proyecto_id: string;
    workflow_id: string | null;
    titulo: string;
    descripcion: string | null;
    fecha_vencimiento: Date | null;
    fecha_completado: Date | null;
    asignado_a: string | null;
    creado_por: string | null;
    created_at: Date | null;
    updated_at: Date | null;
    info_adicional: string | null;
    estado: estado_tarea;
    prioridad: prioridad_tarea | null;
    tarea_padre_id: string | null;
    urgencia: urgencia_tarea_enum | null;
    reunion_id: string | null;
    fecha_inicio: Date | null;
}
export interface TareasInput {
    id?: string;
    proyecto_id: string;
    workflow_id?: string | null;
    titulo: string;
    descripcion?: string | null;
    fecha_vencimiento?: Date | null;
    fecha_completado?: Date | null;
    asignado_a?: string | null;
    creado_por?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    info_adicional?: string | null;
    estado?: estado_tarea;
    prioridad?: prioridad_tarea | null;
    tarea_padre_id?: string | null;
    urgencia?: urgencia_tarea_enum | null;
    reunion_id?: string | null;
    fecha_inicio?: Date | null;
}
export interface TareasClientes {
    id: string;
    proceso_cliente_id: string;
    nombre_tarea_cliente: string;
    descripcion_tarea_cliente: string | null;
    duracion_minutos_por_ejecucion: number | null;
    frecuencia_periodo: tipo_frecuencia_periodo | null;
    es_manual_cliente: boolean | null;
    herramientas_utilizadas_cliente: Json | null;
    puntos_dolor_cliente: string | null;
    oportunidades_mejora_cliente: string | null;
    info_adicional: string | null;
    created_at: Date;
    updated_at: Date;
    frecuencia_ocurrencias: number | null;
}
export interface TareasClientesInput {
    id?: string;
    proceso_cliente_id: string;
    nombre_tarea_cliente: string;
    descripcion_tarea_cliente?: string | null;
    duracion_minutos_por_ejecucion?: number | null;
    frecuencia_periodo?: tipo_frecuencia_periodo | null;
    es_manual_cliente?: boolean | null;
    herramientas_utilizadas_cliente?: Json | null;
    puntos_dolor_cliente?: string | null;
    oportunidades_mejora_cliente?: string | null;
    info_adicional?: string | null;
    created_at?: Date;
    updated_at?: Date;
    frecuencia_ocurrencias?: number | null;
}
export interface TareasClientesResponsables {
    id: string;
    tarea_cliente_id: string;
    persona_cliente_id: string;
    created_at: Date;
    updated_at: Date;
}
export interface TareasClientesResponsablesInput {
    id?: string;
    tarea_cliente_id: string;
    persona_cliente_id: string;
    created_at?: Date;
    updated_at?: Date;
}
export interface TareasEmpresas {
    tarea_id: string;
    empresa_id: string;
}
export interface TareasEmpresasInput {
    tarea_id: string;
    empresa_id: string;
}
export interface TareasEtiquetas {
    tarea_id: string;
    etiqueta_id: string;
}
export interface TareasEtiquetasInput {
    tarea_id: string;
    etiqueta_id: string;
}
export interface ThreadMessageCounts {
    thread_id: number | null;
    message_count: number | null;
    last_message_at: Date | null;
    first_message_at: Date | null;
}
export interface ThreadMessageCountsInput {
    thread_id?: number | null;
    message_count?: number | null;
    last_message_at?: Date | null;
    first_message_at?: Date | null;
}
export interface Threads {
    thread_id: number;
    created_at: Date;
    content: string | null;
    type: string | null;
    from: string | null;
    message_id: number | null;
    agent_id: string | null;
    user_id: string | null;
    id: string;
    request_type: thread_request_type_enum | null;
    input_token_cost: number | null;
    output_token_cost: number | null;
}
export interface ThreadsInput {
    thread_id: number;
    created_at?: Date;
    content?: string | null;
    type?: string | null;
    from?: string | null;
    message_id?: number | null;
    agent_id?: string | null;
    user_id?: string | null;
    id?: string;
    request_type?: thread_request_type_enum | null;
    input_token_cost?: number | null;
    output_token_cost?: number | null;
}
export interface ThreadsMetadata {
    thread_id: number;
    titulo: string | null;
    created_at: Date | null;
    updated_at: Date | null;
}
export interface ThreadsMetadataInput {
    thread_id: number;
    titulo?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
}
export interface ThreadsResumen {
    id: string;
    thread_id_procesado: string;
    titulo: string | null;
    resumen: string | null;
    tools_utilizadas: string | null;
    observaciones_agente: string | null;
    fecha_resumen: Date | null;
    db_modifications_summary: string | null;
}
export interface ThreadsResumenInput {
    id?: string;
    thread_id_procesado: string;
    titulo?: string | null;
    resumen?: string | null;
    tools_utilizadas?: string | null;
    observaciones_agente?: string | null;
    fecha_resumen?: Date | null;
    db_modifications_summary?: string | null;
}
export interface TicketMensajes {
    id: string;
    ticket_id: string;
    usuario_id: string | null;
    contenido: string;
    tipo: string;
    es_privado: boolean | null;
    adjunto_url: string | null;
    created_at: Date | null;
}
export interface TicketMensajesInput {
    id?: string;
    ticket_id: string;
    usuario_id?: string | null;
    contenido: string;
    tipo: string;
    es_privado?: boolean | null;
    adjunto_url?: string | null;
    created_at?: Date | null;
}
export interface Tickets {
    id: string;
    empresa_id: string;
    proyecto_id: string | null;
    workflow_id: string | null;
    contacto_id: string | null;
    titulo: string;
    descripcion: string | null;
    detalles_tecnicos: string | null;
    reproducibilidad: string | null;
    estado: string;
    prioridad: string;
    fecha_resolucion: Date | null;
    asignado_a: string | null;
    resolucion_descripcion: string | null;
    canal_origen: string | null;
    created_at: Date | null;
    updated_at: Date | null;
}
export interface TicketsInput {
    id?: string;
    empresa_id: string;
    proyecto_id?: string | null;
    workflow_id?: string | null;
    contacto_id?: string | null;
    titulo: string;
    descripcion?: string | null;
    detalles_tecnicos?: string | null;
    reproducibilidad?: string | null;
    estado: string;
    prioridad: string;
    fecha_resolucion?: Date | null;
    asignado_a?: string | null;
    resolucion_descripcion?: string | null;
    canal_origen?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
}
export interface Tools {
    id: string;
    tool_name: string;
    tool_description: string | null;
    tool_config: string;
    created_at: Date | null;
    updated_at: Date | null;
}
export interface ToolsInput {
    id?: string;
    tool_name: string;
    tool_description?: string | null;
    tool_config: string;
    created_at?: Date | null;
    updated_at?: Date | null;
}
export interface Usuarios {
    id: string;
    email: string;
    nombre: string;
    rol: string;
    empresa_id: string | null;
    avatar_url: string | null;
    ultimo_acceso: Date | null;
    created_at: Date | null;
    actualizado_en: Date | null;
    info_adicional: string | null;
    apellidos: string | null;
    auth_user_id: string;
    puntos: number;
    racha_actual: number | null;
    ultima_actividad_racha: Date | null;
}
export interface UsuariosInput {
    id?: string;
    email: string;
    nombre: string;
    rol: string;
    empresa_id?: string | null;
    avatar_url?: string | null;
    ultimo_acceso?: Date | null;
    created_at?: Date | null;
    actualizado_en?: Date | null;
    info_adicional?: string | null;
    apellidos?: string | null;
    auth_user_id: string;
    puntos?: number;
    racha_actual?: number | null;
    ultima_actividad_racha?: Date | null;
}
export interface WorkflowErrores {
    id: string;
    workflow_id: string | null;
    tipo_error: string;
    mensaje: string;
    detalles: Json | null;
    stack_trace: string | null;
    fecha_error: Date | null;
    estado: string;
    notificado: boolean | null;
    resuelto_por: string | null;
    fecha_resolucion: Date | null;
    notas_resolucion: string | null;
    created_at: Date | null;
    updated_at: Date | null;
    n8n_workflow_id: string;
}
export interface WorkflowErroresInput {
    id?: string;
    workflow_id?: string | null;
    tipo_error: string;
    mensaje: string;
    detalles?: Json | null;
    stack_trace?: string | null;
    fecha_error?: Date | null;
    estado: string;
    notificado?: boolean | null;
    resuelto_por?: string | null;
    fecha_resolucion?: Date | null;
    notas_resolucion?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    n8n_workflow_id: string;
}
export interface Workflows {
    id: string;
    n8n_workflow_id: string | null;
    nombre: string;
    descripcion: string | null;
    proyecto_id: string | null;
    json_configuracion: Json | null;
    created_at: Date | null;
    updated_at: Date | null;
    etiquetas: Json | null;
    system_prompt_plantilla: string | null;
    user_prompt_plantilla: string | null;
    system_prompt_mejorado: string | null;
    user_prompt_mejorado: string | null;
}
export interface WorkflowsInput {
    id?: string;
    n8n_workflow_id?: string | null;
    nombre: string;
    descripcion?: string | null;
    proyecto_id?: string | null;
    json_configuracion?: Json | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    etiquetas?: Json | null;
    system_prompt_plantilla?: string | null;
    user_prompt_plantilla?: string | null;
    system_prompt_mejorado?: string | null;
    user_prompt_mejorado?: string | null;
}
export interface TableTypes {
    agentes: {
        select: Agentes;
        input: AgentesInput;
    };
    agentes_companeros: {
        select: AgentesCompaneros;
        input: AgentesCompanerosInput;
    };
    agentes_sops_y_guias: {
        select: AgentesSopsYGuias;
        input: AgentesSopsYGuiasInput;
    };
    agentes_tareas: {
        select: AgentesTareas;
        input: AgentesTareasInput;
    };
    agentes_tools: {
        select: AgentesTools;
        input: AgentesToolsInput;
    };
    chat_performance_metrics: {
        select: ChatPerformanceMetrics;
        input: ChatPerformanceMetricsInput;
    };
    comunicaciones: {
        select: Comunicaciones;
        input: ComunicacionesInput;
    };
    configuracion: {
        select: Configuracion;
        input: ConfiguracionInput;
    };
    contratos: {
        select: Contratos;
        input: ContratosInput;
    };
    departamentos: {
        select: Departamentos;
        input: DepartamentosInput;
    };
    diagnosticos: {
        select: Diagnosticos;
        input: DiagnosticosInput;
    };
    doc_exports: {
        select: DocExports;
        input: DocExportsInput;
    };
    documentos_empresa: {
        select: DocumentosEmpresa;
        input: DocumentosEmpresaInput;
    };
    empresas: {
        select: Empresas;
        input: EmpresasInput;
    };
    etiquetas: {
        select: Etiquetas;
        input: EtiquetasInput;
    };
    evaluaciones: {
        select: Evaluaciones;
        input: EvaluacionesInput;
    };
    facturas: {
        select: Facturas;
        input: FacturasInput;
    };
    grabaciones: {
        select: Grabaciones;
        input: GrabacionesInput;
    };
    hallazgos_clientes: {
        select: HallazgosClientes;
        input: HallazgosClientesInput;
    };
    hallazgos_reuniones_departamentos: {
        select: HallazgosReunionesDepartamentos;
        input: HallazgosReunionesDepartamentosInput;
    };
    human_intervention_requests: {
        select: HumanInterventionRequests;
        input: HumanInterventionRequestsInput;
    };
    ideas: {
        select: Ideas;
        input: IdeasInput;
    };
    llm_models: {
        select: LlmModels;
        input: LlmModelsInput;
    };
    llm_providers: {
        select: LlmProviders;
        input: LlmProvidersInput;
    };
    logs_sistema: {
        select: LogsSistema;
        input: LogsSistemaInput;
    };
    mejoras_agentes: {
        select: MejorasAgentes;
        input: MejorasAgentesInput;
    };
    notificaciones: {
        select: Notificaciones;
        input: NotificacionesInput;
    };
    oportunidades: {
        select: Oportunidades;
        input: OportunidadesInput;
    };
    personas: {
        select: Personas;
        input: PersonasInput;
    };
    plantillas_tareas: {
        select: PlantillasTareas;
        input: PlantillasTareasInput;
    };
    preguntas: {
        select: Preguntas;
        input: PreguntasInput;
    };
    procesos: {
        select: Procesos;
        input: ProcesosInput;
    };
    procesos_clientes: {
        select: ProcesosClientes;
        input: ProcesosClientesInput;
    };
    procesos_clientes_departamentos: {
        select: ProcesosClientesDepartamentos;
        input: ProcesosClientesDepartamentosInput;
    };
    procesos_clientes_responsables: {
        select: ProcesosClientesResponsables;
        input: ProcesosClientesResponsablesInput;
    };
    procesos_fuentes_informacion: {
        select: ProcesosFuentesInformacion;
        input: ProcesosFuentesInformacionInput;
    };
    procesos_plantillas: {
        select: ProcesosPlantillas;
        input: ProcesosPlantillasInput;
    };
    procesos_tareas_plantilla: {
        select: ProcesosTareasPlantilla;
        input: ProcesosTareasPlantillaInput;
    };
    proyecto_personas: {
        select: ProyectoPersonas;
        input: ProyectoPersonasInput;
    };
    proyectos: {
        select: Proyectos;
        input: ProyectosInput;
    };
    proyectos_empresas: {
        select: ProyectosEmpresas;
        input: ProyectosEmpresasInput;
    };
    proyectos_procesos: {
        select: ProyectosProcesos;
        input: ProyectosProcesosInput;
    };
    retos_subtareas: {
        select: RetosSubtareas;
        input: RetosSubtareasInput;
    };
    retos_usuarios: {
        select: RetosUsuarios;
        input: RetosUsuariosInput;
    };
    reunion_empresas_asociadas: {
        select: ReunionEmpresasAsociadas;
        input: ReunionEmpresasAsociadasInput;
    };
    reunion_personas_asociadas: {
        select: ReunionPersonasAsociadas;
        input: ReunionPersonasAsociadasInput;
    };
    reunion_speaker_asignaciones: {
        select: ReunionSpeakerAsignaciones;
        input: ReunionSpeakerAsignacionesInput;
    };
    reuniones: {
        select: Reuniones;
        input: ReunionesInput;
    };
    sops_y_guias: {
        select: SopsYGuias;
        input: SopsYGuiasInput;
    };
    tareas: {
        select: Tareas;
        input: TareasInput;
    };
    tareas_clientes: {
        select: TareasClientes;
        input: TareasClientesInput;
    };
    tareas_clientes_responsables: {
        select: TareasClientesResponsables;
        input: TareasClientesResponsablesInput;
    };
    tareas_empresas: {
        select: TareasEmpresas;
        input: TareasEmpresasInput;
    };
    tareas_etiquetas: {
        select: TareasEtiquetas;
        input: TareasEtiquetasInput;
    };
    thread_message_counts: {
        select: ThreadMessageCounts;
        input: ThreadMessageCountsInput;
    };
    threads: {
        select: Threads;
        input: ThreadsInput;
    };
    threads_metadata: {
        select: ThreadsMetadata;
        input: ThreadsMetadataInput;
    };
    threads_resumen: {
        select: ThreadsResumen;
        input: ThreadsResumenInput;
    };
    ticket_mensajes: {
        select: TicketMensajes;
        input: TicketMensajesInput;
    };
    tickets: {
        select: Tickets;
        input: TicketsInput;
    };
    tools: {
        select: Tools;
        input: ToolsInput;
    };
    usuarios: {
        select: Usuarios;
        input: UsuariosInput;
    };
    workflow_errores: {
        select: WorkflowErrores;
        input: WorkflowErroresInput;
    };
    workflows: {
        select: Workflows;
        input: WorkflowsInput;
    };
}
export declare const tables: {
    agentes: {
        readonly tableName: "agentes";
        readonly columns: readonly ["id", "nombre", "descripcion", "system_prompt", "activo", "created_at", "updated_at", "selected_llm_model_id", "db_schema", "temperature"];
        readonly requiredForInsert: readonly ["nombre", "system_prompt"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly selected_llm_model_id: {
                readonly table: "llm_models";
                readonly column: "id";
                readonly $type: LlmModels;
            };
        };
        readonly $type: Agentes;
        readonly $input: AgentesInput;
    };
    agentes_companeros: {
        readonly tableName: "agentes_companeros";
        readonly columns: readonly ["agente_id", "companero_id", "created_at", "id"];
        readonly requiredForInsert: readonly ["agente_id", "companero_id"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly agente_id: {
                readonly table: "agentes";
                readonly column: "id";
                readonly $type: Agentes;
            };
            readonly companero_id: {
                readonly table: "agentes";
                readonly column: "id";
                readonly $type: Agentes;
            };
        };
        readonly $type: AgentesCompaneros;
        readonly $input: AgentesCompanerosInput;
    };
    agentes_sops_y_guias: {
        readonly tableName: "agentes_sops_y_guias";
        readonly columns: readonly ["id", "agente_id", "sop_guia_id", "created_at"];
        readonly requiredForInsert: readonly ["agente_id", "sop_guia_id"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly agente_id: {
                readonly table: "agentes";
                readonly column: "id";
                readonly $type: Agentes;
            };
            readonly sop_guia_id: {
                readonly table: "sops_y_guias";
                readonly column: "id";
                readonly $type: SopsYGuias;
            };
        };
        readonly $type: AgentesSopsYGuias;
        readonly $input: AgentesSopsYGuiasInput;
    };
    agentes_tareas: {
        readonly tableName: "agentes_tareas";
        readonly columns: readonly ["id", "agente_id", "descripcion", "input_data", "estado", "resultado", "prioridad", "tarea_origen_id", "tarea_origen_tipo", "creado_por_tipo", "creado_por_id", "iniciado_en", "finalizado_en", "created_at", "updated_at", "langgraph_thread_id", "titulo", "error_mensaje", "error_detalles"];
        readonly requiredForInsert: readonly ["agente_id", "descripcion", "creado_por_tipo", "creado_por_id", "titulo"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly agente_id: {
                readonly table: "agentes";
                readonly column: "id";
                readonly $type: Agentes;
            };
        };
        readonly $type: AgentesTareas;
        readonly $input: AgentesTareasInput;
    };
    agentes_tools: {
        readonly tableName: "agentes_tools";
        readonly columns: readonly ["agente_id", "tool_id", "created_at", "id"];
        readonly requiredForInsert: readonly ["agente_id", "tool_id"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly agente_id: {
                readonly table: "agentes";
                readonly column: "id";
                readonly $type: Agentes;
            };
            readonly tool_id: {
                readonly table: "tools";
                readonly column: "id";
                readonly $type: Tools;
            };
        };
        readonly $type: AgentesTools;
        readonly $input: AgentesToolsInput;
    };
    chat_performance_metrics: {
        readonly tableName: "chat_performance_metrics";
        readonly columns: readonly ["thread_id", "total_messages", "user_messages", "agent_answers", "intermediate_steps", "last_activity", "first_activity", "conversation_duration_hours", "avg_message_length", "total_content_length"];
        readonly requiredForInsert: readonly [];
        readonly primaryKey: null;
        readonly foreignKeys: {};
        readonly $type: ChatPerformanceMetrics;
        readonly $input: ChatPerformanceMetricsInput;
    };
    comunicaciones: {
        readonly tableName: "comunicaciones";
        readonly columns: readonly ["id", "tipo", "fecha_hora", "direccion", "asunto", "contenido", "transcripcion_url", "duracion_minutos", "participante_usuario_id", "participante_persona_id", "participante_externo_detalle", "relacionado_oportunidad_id", "relacionado_empresa_id", "relacionado_proyecto_id", "relacionado_tarea_id", "fuente_sistema", "created_at"];
        readonly requiredForInsert: readonly ["tipo"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly participante_usuario_id: {
                readonly table: "usuarios";
                readonly column: "id";
                readonly $type: Usuarios;
            };
            readonly participante_persona_id: {
                readonly table: "personas";
                readonly column: "id";
                readonly $type: Personas;
            };
            readonly relacionado_oportunidad_id: {
                readonly table: "oportunidades";
                readonly column: "id";
                readonly $type: Oportunidades;
            };
            readonly relacionado_empresa_id: {
                readonly table: "empresas";
                readonly column: "id";
                readonly $type: Empresas;
            };
            readonly relacionado_proyecto_id: {
                readonly table: "proyectos";
                readonly column: "id";
                readonly $type: Proyectos;
            };
            readonly relacionado_tarea_id: {
                readonly table: "tareas";
                readonly column: "id";
                readonly $type: Tareas;
            };
        };
        readonly $type: Comunicaciones;
        readonly $input: ComunicacionesInput;
    };
    configuracion: {
        readonly tableName: "configuracion";
        readonly columns: readonly ["id", "clave", "valor", "descripcion", "created_at", "updated_at"];
        readonly requiredForInsert: readonly ["clave", "valor"];
        readonly primaryKey: "id";
        readonly foreignKeys: {};
        readonly $type: Configuracion;
        readonly $input: ConfiguracionInput;
    };
    contratos: {
        readonly tableName: "contratos";
        readonly columns: readonly ["id", "codigo", "titulo", "empresa_id", "proyecto_id", "fecha_inicio", "fecha_fin", "renovacion_automatica", "periodo_renovacion", "valor_contrato", "moneda", "periodicidad_facturacion", "dia_facturacion", "estado", "fecha_firma", "firmado_cliente", "firmado_empresa", "url_documento", "persona_firma_id", "terminos_condiciones", "clausulas", "fecha_cancelacion", "motivo_cancelacion", "contrato_relacionado_id", "info_adicional", "creado_por", "created_at", "updated_at"];
        readonly requiredForInsert: readonly ["codigo", "titulo", "empresa_id", "fecha_inicio"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly empresa_id: {
                readonly table: "empresas";
                readonly column: "id";
                readonly $type: Empresas;
            };
            readonly proyecto_id: {
                readonly table: "proyectos";
                readonly column: "id";
                readonly $type: Proyectos;
            };
            readonly contrato_relacionado_id: {
                readonly table: "contratos";
                readonly column: "id";
                readonly $type: Contratos;
            };
        };
        readonly $type: Contratos;
        readonly $input: ContratosInput;
    };
    departamentos: {
        readonly tableName: "departamentos";
        readonly columns: readonly ["id", "empresa_id", "nombre", "descripcion", "created_at", "updated_at", "info_adicional"];
        readonly requiredForInsert: readonly ["empresa_id", "nombre"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly empresa_id: {
                readonly table: "empresas";
                readonly column: "id";
                readonly $type: Empresas;
            };
        };
        readonly $type: Departamentos;
        readonly $input: DepartamentosInput;
    };
    diagnosticos: {
        readonly tableName: "diagnosticos";
        readonly columns: readonly ["id", "empresa_id", "titulo", "fecha_generacion", "diagrama_reactflow", "plan_aceleracion_html", "estado", "version", "creado_por", "created_at", "updated_at", "diagrama_proceso_json"];
        readonly requiredForInsert: readonly [];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly empresa_id: {
                readonly table: "empresas";
                readonly column: "id";
                readonly $type: Empresas;
            };
            readonly creado_por: {
                readonly table: "usuarios";
                readonly column: "id";
                readonly $type: Usuarios;
            };
        };
        readonly $type: Diagnosticos;
        readonly $input: DiagnosticosInput;
    };
    doc_exports: {
        readonly tableName: "doc_exports";
        readonly columns: readonly ["id", "threads_row_id", "user_id", "status", "doc_url", "created_at", "updated_at"];
        readonly requiredForInsert: readonly ["threads_row_id", "user_id", "status"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly threads_row_id: {
                readonly table: "threads";
                readonly column: "id";
                readonly $type: Threads;
            };
            readonly user_id: {
                readonly table: "users";
                readonly column: "id";
                readonly $type: unknown;
            };
        };
        readonly $type: DocExports;
        readonly $input: DocExportsInput;
    };
    documentos_empresa: {
        readonly tableName: "documentos_empresa";
        readonly columns: readonly ["id", "nombre_documento", "url_documento", "ruta_carpeta_drive", "resumen", "usuario_documento_id", "creado_por_agente_id", "empresa_relacionada_id", "proyecto_relacionado_id", "proceso_relacionado_id", "info_adicional", "created_at", "updated_at"];
        readonly requiredForInsert: readonly [];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly usuario_documento_id: {
                readonly table: "usuarios";
                readonly column: "id";
                readonly $type: Usuarios;
            };
            readonly creado_por_agente_id: {
                readonly table: "agentes";
                readonly column: "id";
                readonly $type: Agentes;
            };
            readonly empresa_relacionada_id: {
                readonly table: "empresas";
                readonly column: "id";
                readonly $type: Empresas;
            };
            readonly proyecto_relacionado_id: {
                readonly table: "proyectos";
                readonly column: "id";
                readonly $type: Proyectos;
            };
            readonly proceso_relacionado_id: {
                readonly table: "procesos";
                readonly column: "id";
                readonly $type: Procesos;
            };
        };
        readonly $type: DocumentosEmpresa;
        readonly $input: DocumentosEmpresaInput;
    };
    empresas: {
        readonly tableName: "empresas";
        readonly columns: readonly ["id", "nombre", "sector", "logo_url", "direccion", "telefono", "email_principal", "website", "fecha_alta", "activo", "created_at", "updated_at", "info_adicional", "nif_cif", "direccion_fiscal", "tipo_empresa", "descripcion", "tipo_relacion"];
        readonly requiredForInsert: readonly ["nombre"];
        readonly primaryKey: "id";
        readonly foreignKeys: {};
        readonly $type: Empresas;
        readonly $input: EmpresasInput;
    };
    etiquetas: {
        readonly tableName: "etiquetas";
        readonly columns: readonly ["id", "nombre", "color", "descripcion", "created_at", "updated_at"];
        readonly requiredForInsert: readonly ["nombre"];
        readonly primaryKey: "id";
        readonly foreignKeys: {};
        readonly $type: Etiquetas;
        readonly $input: EtiquetasInput;
    };
    evaluaciones: {
        readonly tableName: "evaluaciones";
        readonly columns: readonly ["id", "created_at", "nombre_agente_workflow", "proposito_agente", "criterios_adicionales_evaluacion", "agente_output", "puntuacion", "argumentos_puntuacion", "sugerencias_mejora", "execution_id", "n8n_workflow_id", "estado", "agente_id", "nombre_agente_amigable", "mejora_agente_id", "user_id"];
        readonly requiredForInsert: readonly ["id"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly mejora_agente_id: {
                readonly table: "mejoras_agentes";
                readonly column: "id";
                readonly $type: MejorasAgentes;
            };
            readonly user_id: {
                readonly table: "usuarios";
                readonly column: "auth_user_id";
                readonly $type: Usuarios;
            };
        };
        readonly $type: Evaluaciones;
        readonly $input: EvaluacionesInput;
    };
    facturas: {
        readonly tableName: "facturas";
        readonly columns: readonly ["id", "numero_factura", "serie", "empresa_id", "proyecto_id", "contrato_id", "fecha_emision", "fecha_vencimiento", "subtotal", "iva", "total", "estado", "fecha_pago", "metodo_pago", "referencia_pago", "pdf_url", "xml_url", "plantilla_id", "emisor_nombre", "emisor_nif", "emisor_direccion", "receptor_nombre", "receptor_nif", "receptor_direccion", "items", "recordatorios_enviados", "fecha_ultimo_recordatorio", "info_adicional", "creado_por", "created_at", "updated_at"];
        readonly requiredForInsert: readonly ["numero_factura", "empresa_id", "fecha_vencimiento", "subtotal", "iva", "total", "emisor_nombre", "emisor_nif", "emisor_direccion", "receptor_nombre", "receptor_nif", "receptor_direccion"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly empresa_id: {
                readonly table: "empresas";
                readonly column: "id";
                readonly $type: Empresas;
            };
            readonly proyecto_id: {
                readonly table: "proyectos";
                readonly column: "id";
                readonly $type: Proyectos;
            };
            readonly contrato_id: {
                readonly table: "contratos";
                readonly column: "id";
                readonly $type: Contratos;
            };
        };
        readonly $type: Facturas;
        readonly $input: FacturasInput;
    };
    grabaciones: {
        readonly tableName: "grabaciones";
        readonly columns: readonly ["id", "entidad_relacionada_id", "entidad_relacionada_tipo", "usuario_id", "url_almacenamiento", "estado_procesamiento", "tipo_grabacion", "transcripcion", "duracion_segundos", "info_adicional", "created_at", "updated_at"];
        readonly requiredForInsert: readonly ["entidad_relacionada_id", "entidad_relacionada_tipo", "usuario_id", "url_almacenamiento", "tipo_grabacion"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly usuario_id: {
                readonly table: "usuarios";
                readonly column: "id";
                readonly $type: Usuarios;
            };
        };
        readonly $type: Grabaciones;
        readonly $input: GrabacionesInput;
    };
    hallazgos_clientes: {
        readonly tableName: "hallazgos_clientes";
        readonly columns: readonly ["id", "reunion_id", "empresa_id", "persona_id", "tipo", "descripcion", "impacto", "posible_solucion", "estado", "created_at", "updated_at", "procesos_relacionados", "titulo"];
        readonly requiredForInsert: readonly ["reunion_id", "empresa_id", "titulo"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly reunion_id: {
                readonly table: "reuniones";
                readonly column: "id";
                readonly $type: Reuniones;
            };
            readonly empresa_id: {
                readonly table: "empresas";
                readonly column: "id";
                readonly $type: Empresas;
            };
            readonly persona_id: {
                readonly table: "personas";
                readonly column: "id";
                readonly $type: Personas;
            };
        };
        readonly $type: HallazgosClientes;
        readonly $input: HallazgosClientesInput;
    };
    hallazgos_reuniones_departamentos: {
        readonly tableName: "hallazgos_reuniones_departamentos";
        readonly columns: readonly ["hallazgo_reunion_id", "departamento_id", "created_at"];
        readonly requiredForInsert: readonly ["hallazgo_reunion_id", "departamento_id"];
        readonly primaryKey: "hallazgo_reunion_id";
        readonly foreignKeys: {
            readonly hallazgo_reunion_id: {
                readonly table: "hallazgos_clientes";
                readonly column: "id";
                readonly $type: HallazgosClientes;
            };
            readonly departamento_id: {
                readonly table: "departamentos";
                readonly column: "id";
                readonly $type: Departamentos;
            };
        };
        readonly $type: HallazgosReunionesDepartamentos;
        readonly $input: HallazgosReunionesDepartamentosInput;
    };
    human_intervention_requests: {
        readonly tableName: "human_intervention_requests";
        readonly columns: readonly ["id", "langgraph_thread_id", "asistente_id", "request_type", "request_details", "status", "resolution_data", "requested_at", "resolved_at", "resolved_by", "created_at", "updated_at", "triggering_tool_call_id"];
        readonly requiredForInsert: readonly ["langgraph_thread_id", "asistente_id", "request_type"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly asistente_id: {
                readonly table: "agentes";
                readonly column: "id";
                readonly $type: Agentes;
            };
            readonly resolved_by: {
                readonly table: "usuarios";
                readonly column: "id";
                readonly $type: Usuarios;
            };
        };
        readonly $type: HumanInterventionRequests;
        readonly $input: HumanInterventionRequestsInput;
    };
    ideas: {
        readonly tableName: "ideas";
        readonly columns: readonly ["id", "titulo", "descripcion", "empresa_relacionada_id", "proyecto_relacionado_id", "created_at", "updated_at", "estado", "prioridad"];
        readonly requiredForInsert: readonly ["titulo"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly empresa_relacionada_id: {
                readonly table: "empresas";
                readonly column: "id";
                readonly $type: Empresas;
            };
            readonly proyecto_relacionado_id: {
                readonly table: "proyectos";
                readonly column: "id";
                readonly $type: Proyectos;
            };
        };
        readonly $type: Ideas;
        readonly $input: IdeasInput;
    };
    llm_models: {
        readonly tableName: "llm_models";
        readonly columns: readonly ["id", "provider_id", "model_name", "description", "context_window", "active", "created_at", "input_cost_per_million_tokens", "output_cost_per_million_tokens"];
        readonly requiredForInsert: readonly ["provider_id", "model_name"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly provider_id: {
                readonly table: "llm_providers";
                readonly column: "id";
                readonly $type: LlmProviders;
            };
        };
        readonly $type: LlmModels;
        readonly $input: LlmModelsInput;
    };
    llm_providers: {
        readonly tableName: "llm_providers";
        readonly columns: readonly ["id", "provider_key", "provider_name", "created_at"];
        readonly requiredForInsert: readonly ["provider_key", "provider_name"];
        readonly primaryKey: "id";
        readonly foreignKeys: {};
        readonly $type: LlmProviders;
        readonly $input: LlmProvidersInput;
    };
    logs_sistema: {
        readonly tableName: "logs_sistema";
        readonly columns: readonly ["id", "tipo", "origen", "mensaje", "detalles", "usuario_id", "timestamp"];
        readonly requiredForInsert: readonly ["tipo", "origen", "mensaje"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly usuario_id: {
                readonly table: "usuarios";
                readonly column: "id";
                readonly $type: Usuarios;
            };
        };
        readonly $type: LogsSistema;
        readonly $input: LogsSistemaInput;
    };
    mejoras_agentes: {
        readonly tableName: "mejoras_agentes";
        readonly columns: readonly ["id", "agente_id", "n8n_workflow_id", "explicacion_mejoras", "estado", "created_at", "updated_at", "system_prompt_original", "user_prompt_original", "system_prompt_mejorado", "user_prompt_mejorado", "set_node_id", "nombre_agente_amigable", "nombre_workflow", "user_id"];
        readonly requiredForInsert: readonly ["explicacion_mejoras", "user_id"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly agente_id: {
                readonly table: "agentes";
                readonly column: "id";
                readonly $type: Agentes;
            };
            readonly user_id: {
                readonly table: "usuarios";
                readonly column: "auth_user_id";
                readonly $type: Usuarios;
            };
        };
        readonly $type: MejorasAgentes;
        readonly $input: MejorasAgentesInput;
    };
    notificaciones: {
        readonly tableName: "notificaciones";
        readonly columns: readonly ["id", "usuario_id", "titulo", "mensaje", "url_destino", "estado", "tipo_notificacion", "created_at"];
        readonly requiredForInsert: readonly ["usuario_id", "titulo", "mensaje", "tipo_notificacion"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly usuario_id: {
                readonly table: "usuarios";
                readonly column: "id";
                readonly $type: Usuarios;
            };
        };
        readonly $type: Notificaciones;
        readonly $input: NotificacionesInput;
    };
    oportunidades: {
        readonly tableName: "oportunidades";
        readonly columns: readonly ["id", "titulo", "descripcion", "empresa_id", "persona_contacto_id", "valor_estimado", "moneda", "etapa", "probabilidad_cierre", "fecha_cierre_estimada", "fecha_cierre_real", "motivo_perdida", "documento_url", "asignado_usuario_id", "created_at", "updated_at"];
        readonly requiredForInsert: readonly ["titulo"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly empresa_id: {
                readonly table: "empresas";
                readonly column: "id";
                readonly $type: Empresas;
            };
            readonly persona_contacto_id: {
                readonly table: "personas";
                readonly column: "id";
                readonly $type: Personas;
            };
            readonly asignado_usuario_id: {
                readonly table: "usuarios";
                readonly column: "id";
                readonly $type: Usuarios;
            };
        };
        readonly $type: Oportunidades;
        readonly $input: OportunidadesInput;
    };
    personas: {
        readonly tableName: "personas";
        readonly columns: readonly ["id", "empresa_id", "departamento_id", "nombre", "apellidos", "email", "telefono", "cargo", "tipo", "es_decision_maker", "entrevistado", "fecha_entrevista", "fecha_alta", "fecha_baja", "activo", "info_adicional", "created_at", "updated_at", "responsable_departamento", "linkedin_url", "usuario_id"];
        readonly requiredForInsert: readonly ["nombre", "tipo"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly empresa_id: {
                readonly table: "empresas";
                readonly column: "id";
                readonly $type: Empresas;
            };
            readonly departamento_id: {
                readonly table: "departamentos";
                readonly column: "id";
                readonly $type: Departamentos;
            };
            readonly usuario_id: {
                readonly table: "usuarios";
                readonly column: "id";
                readonly $type: Usuarios;
            };
        };
        readonly $type: Personas;
        readonly $input: PersonasInput;
    };
    plantillas_tareas: {
        readonly tableName: "plantillas_tareas";
        readonly columns: readonly ["id", "titulo_plantilla", "descripcion_base", "duracion_estimada_horas", "prioridad_predeterminada", "info_adicional_plantilla", "created_at", "updated_at"];
        readonly requiredForInsert: readonly ["titulo_plantilla"];
        readonly primaryKey: "id";
        readonly foreignKeys: {};
        readonly $type: PlantillasTareas;
        readonly $input: PlantillasTareasInput;
    };
    preguntas: {
        readonly tableName: "preguntas";
        readonly columns: readonly ["id", "titulo", "estado", "visibilidad", "destinatario_usuario_id", "origen_reunion_id", "origen_proceso_id", "respuesta_texto", "respuesta_grabacion_id", "info_adicional", "created_at", "updated_at"];
        readonly requiredForInsert: readonly ["titulo"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly destinatario_usuario_id: {
                readonly table: "usuarios";
                readonly column: "id";
                readonly $type: Usuarios;
            };
            readonly origen_reunion_id: {
                readonly table: "reuniones";
                readonly column: "id";
                readonly $type: Reuniones;
            };
            readonly origen_proceso_id: {
                readonly table: "procesos_clientes";
                readonly column: "id";
                readonly $type: ProcesosClientes;
            };
            readonly respuesta_grabacion_id: {
                readonly table: "grabaciones";
                readonly column: "id";
                readonly $type: Grabaciones;
            };
        };
        readonly $type: Preguntas;
        readonly $input: PreguntasInput;
    };
    procesos: {
        readonly tableName: "procesos";
        readonly columns: readonly ["id", "empresa_id", "departamento_id", "nombre", "descripcion", "es_repetitivo", "es_cuello_botella", "es_manual", "valor_negocio", "complejidad_automatizacion", "prioridad_automatizacion", "tiempo_estimado_manual", "frecuencia", "created_at", "updated_at", "info_adicional", "persona_id", "herramientas_utilizadas", "tipo_proceso", "proceso_plantilla_origen_id", "reunion_origen_id"];
        readonly requiredForInsert: readonly ["empresa_id", "nombre"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly empresa_id: {
                readonly table: "empresas";
                readonly column: "id";
                readonly $type: Empresas;
            };
            readonly departamento_id: {
                readonly table: "departamentos";
                readonly column: "id";
                readonly $type: Departamentos;
            };
            readonly persona_id: {
                readonly table: "personas";
                readonly column: "id";
                readonly $type: Personas;
            };
            readonly proceso_plantilla_origen_id: {
                readonly table: "procesos_plantillas";
                readonly column: "id";
                readonly $type: ProcesosPlantillas;
            };
            readonly reunion_origen_id: {
                readonly table: "reuniones";
                readonly column: "id";
                readonly $type: Reuniones;
            };
        };
        readonly $type: Procesos;
        readonly $input: ProcesosInput;
    };
    procesos_clientes: {
        readonly tableName: "procesos_clientes";
        readonly columns: readonly ["id", "empresa_cliente_id", "nombre", "descripcion", "estado_analisis", "info_adicional", "created_at", "updated_at", "reunion_origen_id", "es_repetitivo", "es_cuello_botella", "es_manual", "valor_negocio_cliente", "complejidad_automatizacion_aceleralia", "prioridad_automatizacion_aceleralia", "duracion_minutos_por_ejecucion", "frecuencia_periodo", "herramientas_utilizadas_cliente", "frecuencia_ocurrencias"];
        readonly requiredForInsert: readonly ["empresa_cliente_id", "nombre"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly empresa_cliente_id: {
                readonly table: "empresas";
                readonly column: "id";
                readonly $type: Empresas;
            };
            readonly reunion_origen_id: {
                readonly table: "reuniones";
                readonly column: "id";
                readonly $type: Reuniones;
            };
        };
        readonly $type: ProcesosClientes;
        readonly $input: ProcesosClientesInput;
    };
    procesos_clientes_departamentos: {
        readonly tableName: "procesos_clientes_departamentos";
        readonly columns: readonly ["proceso_cliente_id", "departamento_id", "created_at"];
        readonly requiredForInsert: readonly ["proceso_cliente_id", "departamento_id"];
        readonly primaryKey: "proceso_cliente_id";
        readonly foreignKeys: {
            readonly proceso_cliente_id: {
                readonly table: "procesos_clientes";
                readonly column: "id";
                readonly $type: ProcesosClientes;
            };
            readonly departamento_id: {
                readonly table: "departamentos";
                readonly column: "id";
                readonly $type: Departamentos;
            };
        };
        readonly $type: ProcesosClientesDepartamentos;
        readonly $input: ProcesosClientesDepartamentosInput;
    };
    procesos_clientes_responsables: {
        readonly tableName: "procesos_clientes_responsables";
        readonly columns: readonly ["id", "proceso_cliente_id", "persona_cliente_id", "created_at", "updated_at"];
        readonly requiredForInsert: readonly ["proceso_cliente_id", "persona_cliente_id"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly proceso_cliente_id: {
                readonly table: "procesos_clientes";
                readonly column: "id";
                readonly $type: ProcesosClientes;
            };
            readonly persona_cliente_id: {
                readonly table: "personas";
                readonly column: "id";
                readonly $type: Personas;
            };
        };
        readonly $type: ProcesosClientesResponsables;
        readonly $input: ProcesosClientesResponsablesInput;
    };
    procesos_fuentes_informacion: {
        readonly tableName: "procesos_fuentes_informacion";
        readonly columns: readonly ["id", "proceso_cliente_id", "nombre_informacion", "descripcion", "persona_id", "formato", "url_adjunto", "estado", "fecha_ultima_revision", "created_at", "updated_at"];
        readonly requiredForInsert: readonly ["proceso_cliente_id", "nombre_informacion", "persona_id", "formato", "estado"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly proceso_cliente_id: {
                readonly table: "procesos_clientes";
                readonly column: "id";
                readonly $type: ProcesosClientes;
            };
            readonly persona_id: {
                readonly table: "personas";
                readonly column: "id";
                readonly $type: Personas;
            };
        };
        readonly $type: ProcesosFuentesInformacion;
        readonly $input: ProcesosFuentesInformacionInput;
    };
    procesos_plantillas: {
        readonly tableName: "procesos_plantillas";
        readonly columns: readonly ["id", "nombre_plantilla", "descripcion_plantilla", "objetivo_plantilla", "info_adicional_plantilla", "created_at", "updated_at"];
        readonly requiredForInsert: readonly ["nombre_plantilla"];
        readonly primaryKey: "id";
        readonly foreignKeys: {};
        readonly $type: ProcesosPlantillas;
        readonly $input: ProcesosPlantillasInput;
    };
    procesos_tareas_plantilla: {
        readonly tableName: "procesos_tareas_plantilla";
        readonly columns: readonly ["id", "proceso_plantilla_id", "plantilla_tarea_id", "orden_en_proceso", "dias_desplazamiento", "es_obligatoria", "notas_especificas_proceso", "created_at", "updated_at"];
        readonly requiredForInsert: readonly ["proceso_plantilla_id", "plantilla_tarea_id", "orden_en_proceso"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly proceso_plantilla_id: {
                readonly table: "procesos_plantillas";
                readonly column: "id";
                readonly $type: ProcesosPlantillas;
            };
            readonly plantilla_tarea_id: {
                readonly table: "plantillas_tareas";
                readonly column: "id";
                readonly $type: PlantillasTareas;
            };
        };
        readonly $type: ProcesosTareasPlantilla;
        readonly $input: ProcesosTareasPlantillaInput;
    };
    proyecto_personas: {
        readonly tableName: "proyecto_personas";
        readonly columns: readonly ["id", "proyecto_id", "persona_id", "rol", "asignado_desde", "asignado_hasta", "porcentaje_dedicacion", "es_responsable", "info_adicional", "created_at", "updated_at"];
        readonly requiredForInsert: readonly [];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly proyecto_id: {
                readonly table: "proyectos";
                readonly column: "id";
                readonly $type: Proyectos;
            };
            readonly persona_id: {
                readonly table: "personas";
                readonly column: "id";
                readonly $type: Personas;
            };
        };
        readonly $type: ProyectoPersonas;
        readonly $input: ProyectoPersonasInput;
    };
    proyectos: {
        readonly tableName: "proyectos";
        readonly columns: readonly ["id", "nombre", "descripcion", "objetivo", "estado", "fecha_inicio", "fecha_fin_estimada", "fecha_fin_real", "presupuesto", "created_at", "updated_at", "responsable_persona_id", "info_adicional", "prioridad", "progreso", "responsable_usuario_id"];
        readonly requiredForInsert: readonly ["nombre", "estado"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly responsable_persona_id: {
                readonly table: "personas";
                readonly column: "id";
                readonly $type: Personas;
            };
            readonly responsable_usuario_id: {
                readonly table: "usuarios";
                readonly column: "id";
                readonly $type: Usuarios;
            };
        };
        readonly $type: Proyectos;
        readonly $input: ProyectosInput;
    };
    proyectos_empresas: {
        readonly tableName: "proyectos_empresas";
        readonly columns: readonly ["proyecto_id", "empresa_id"];
        readonly requiredForInsert: readonly ["proyecto_id", "empresa_id"];
        readonly primaryKey: "proyecto_id";
        readonly foreignKeys: {
            readonly proyecto_id: {
                readonly table: "proyectos";
                readonly column: "id";
                readonly $type: Proyectos;
            };
            readonly empresa_id: {
                readonly table: "empresas";
                readonly column: "id";
                readonly $type: Empresas;
            };
        };
        readonly $type: ProyectosEmpresas;
        readonly $input: ProyectosEmpresasInput;
    };
    proyectos_procesos: {
        readonly tableName: "proyectos_procesos";
        readonly columns: readonly ["id", "proyecto_id", "proceso_id", "created_at"];
        readonly requiredForInsert: readonly ["proyecto_id", "proceso_id"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly proyecto_id: {
                readonly table: "proyectos";
                readonly column: "id";
                readonly $type: Proyectos;
            };
            readonly proceso_id: {
                readonly table: "procesos";
                readonly column: "id";
                readonly $type: Procesos;
            };
        };
        readonly $type: ProyectosProcesos;
        readonly $input: ProyectosProcesosInput;
    };
    retos_subtareas: {
        readonly tableName: "retos_subtareas";
        readonly columns: readonly ["id", "reto_usuario_id", "titulo", "descripcion", "tipo_accion", "entidad_relacionada_id", "entidad_relacionada_tipo", "estado", "created_at", "updated_at"];
        readonly requiredForInsert: readonly ["reto_usuario_id", "titulo", "tipo_accion", "entidad_relacionada_id", "entidad_relacionada_tipo"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly reto_usuario_id: {
                readonly table: "retos_usuarios";
                readonly column: "id";
                readonly $type: RetosUsuarios;
            };
        };
        readonly $type: RetosSubtareas;
        readonly $input: RetosSubtareasInput;
    };
    retos_usuarios: {
        readonly tableName: "retos_usuarios";
        readonly columns: readonly ["id", "usuario_id", "titulo", "descripcion", "puntos_recompensa", "estado", "prioridad", "url_destino", "created_at", "updated_at"];
        readonly requiredForInsert: readonly ["usuario_id", "titulo"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly usuario_id: {
                readonly table: "usuarios";
                readonly column: "id";
                readonly $type: Usuarios;
            };
        };
        readonly $type: RetosUsuarios;
        readonly $input: RetosUsuariosInput;
    };
    reunion_empresas_asociadas: {
        readonly tableName: "reunion_empresas_asociadas";
        readonly columns: readonly ["reunion_id", "empresa_id", "created_at"];
        readonly requiredForInsert: readonly ["reunion_id", "empresa_id"];
        readonly primaryKey: "reunion_id";
        readonly foreignKeys: {
            readonly reunion_id: {
                readonly table: "reuniones";
                readonly column: "id";
                readonly $type: Reuniones;
            };
            readonly empresa_id: {
                readonly table: "empresas";
                readonly column: "id";
                readonly $type: Empresas;
            };
        };
        readonly $type: ReunionEmpresasAsociadas;
        readonly $input: ReunionEmpresasAsociadasInput;
    };
    reunion_personas_asociadas: {
        readonly tableName: "reunion_personas_asociadas";
        readonly columns: readonly ["reunion_id", "persona_id", "created_at"];
        readonly requiredForInsert: readonly ["reunion_id", "persona_id"];
        readonly primaryKey: "reunion_id";
        readonly foreignKeys: {
            readonly reunion_id: {
                readonly table: "reuniones";
                readonly column: "id";
                readonly $type: Reuniones;
            };
            readonly persona_id: {
                readonly table: "personas";
                readonly column: "id";
                readonly $type: Personas;
            };
        };
        readonly $type: ReunionPersonasAsociadas;
        readonly $input: ReunionPersonasAsociadasInput;
    };
    reunion_speaker_asignaciones: {
        readonly tableName: "reunion_speaker_asignaciones";
        readonly columns: readonly ["id", "reunion_id", "speaker_tag", "asignado_a_tipo", "asignado_a_id", "nombre_asignado", "created_at", "updated_at"];
        readonly requiredForInsert: readonly ["reunion_id", "speaker_tag", "asignado_a_tipo", "asignado_a_id"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly reunion_id: {
                readonly table: "reuniones";
                readonly column: "id";
                readonly $type: Reuniones;
            };
        };
        readonly $type: ReunionSpeakerAsignaciones;
        readonly $input: ReunionSpeakerAsignacionesInput;
    };
    reuniones: {
        readonly tableName: "reuniones";
        readonly columns: readonly ["id", "user_id", "titulo", "observaciones_iniciales", "url_grabacion_original", "url_grabacion_publica", "fecha_reunion", "transcripcion_raw", "transcripcion_final", "resumen", "puntos_clave", "estado_procesamiento", "info_adicional", "created_at", "updated_at", "entrevista", "video", "duracion_minutos"];
        readonly requiredForInsert: readonly [];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly user_id: {
                readonly table: "usuarios";
                readonly column: "id";
                readonly $type: Usuarios;
            };
        };
        readonly $type: Reuniones;
        readonly $input: ReunionesInput;
    };
    sops_y_guias: {
        readonly tableName: "sops_y_guias";
        readonly columns: readonly ["id", "titulo", "descripcion", "contenido_markdown", "dueno_sop_id", "created_at", "updated_at", "type"];
        readonly requiredForInsert: readonly ["titulo", "contenido_markdown"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly dueno_sop_id: {
                readonly table: "usuarios";
                readonly column: "id";
                readonly $type: Usuarios;
            };
        };
        readonly $type: SopsYGuias;
        readonly $input: SopsYGuiasInput;
    };
    tareas: {
        readonly tableName: "tareas";
        readonly columns: readonly ["id", "proyecto_id", "workflow_id", "titulo", "descripcion", "fecha_vencimiento", "fecha_completado", "asignado_a", "creado_por", "created_at", "updated_at", "info_adicional", "estado", "prioridad", "tarea_padre_id", "urgencia", "reunion_id", "fecha_inicio"];
        readonly requiredForInsert: readonly ["proyecto_id", "titulo"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly proyecto_id: {
                readonly table: "proyectos";
                readonly column: "id";
                readonly $type: Proyectos;
            };
            readonly workflow_id: {
                readonly table: "workflows";
                readonly column: "id";
                readonly $type: Workflows;
            };
            readonly asignado_a: {
                readonly table: "usuarios";
                readonly column: "id";
                readonly $type: Usuarios;
            };
            readonly creado_por: {
                readonly table: "usuarios";
                readonly column: "id";
                readonly $type: Usuarios;
            };
            readonly tarea_padre_id: {
                readonly table: "tareas";
                readonly column: "id";
                readonly $type: Tareas;
            };
            readonly reunion_id: {
                readonly table: "reuniones";
                readonly column: "id";
                readonly $type: Reuniones;
            };
        };
        readonly $type: Tareas;
        readonly $input: TareasInput;
    };
    tareas_clientes: {
        readonly tableName: "tareas_clientes";
        readonly columns: readonly ["id", "proceso_cliente_id", "nombre_tarea_cliente", "descripcion_tarea_cliente", "duracion_minutos_por_ejecucion", "frecuencia_periodo", "es_manual_cliente", "herramientas_utilizadas_cliente", "puntos_dolor_cliente", "oportunidades_mejora_cliente", "info_adicional", "created_at", "updated_at", "frecuencia_ocurrencias"];
        readonly requiredForInsert: readonly ["proceso_cliente_id", "nombre_tarea_cliente"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly proceso_cliente_id: {
                readonly table: "procesos_clientes";
                readonly column: "id";
                readonly $type: ProcesosClientes;
            };
        };
        readonly $type: TareasClientes;
        readonly $input: TareasClientesInput;
    };
    tareas_clientes_responsables: {
        readonly tableName: "tareas_clientes_responsables";
        readonly columns: readonly ["id", "tarea_cliente_id", "persona_cliente_id", "created_at", "updated_at"];
        readonly requiredForInsert: readonly ["tarea_cliente_id", "persona_cliente_id"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly tarea_cliente_id: {
                readonly table: "tareas_clientes";
                readonly column: "id";
                readonly $type: TareasClientes;
            };
            readonly persona_cliente_id: {
                readonly table: "personas";
                readonly column: "id";
                readonly $type: Personas;
            };
        };
        readonly $type: TareasClientesResponsables;
        readonly $input: TareasClientesResponsablesInput;
    };
    tareas_empresas: {
        readonly tableName: "tareas_empresas";
        readonly columns: readonly ["tarea_id", "empresa_id"];
        readonly requiredForInsert: readonly ["tarea_id", "empresa_id"];
        readonly primaryKey: "tarea_id";
        readonly foreignKeys: {
            readonly tarea_id: {
                readonly table: "tareas";
                readonly column: "id";
                readonly $type: Tareas;
            };
            readonly empresa_id: {
                readonly table: "empresas";
                readonly column: "id";
                readonly $type: Empresas;
            };
        };
        readonly $type: TareasEmpresas;
        readonly $input: TareasEmpresasInput;
    };
    tareas_etiquetas: {
        readonly tableName: "tareas_etiquetas";
        readonly columns: readonly ["tarea_id", "etiqueta_id"];
        readonly requiredForInsert: readonly ["tarea_id", "etiqueta_id"];
        readonly primaryKey: "tarea_id";
        readonly foreignKeys: {
            readonly tarea_id: {
                readonly table: "tareas";
                readonly column: "id";
                readonly $type: Tareas;
            };
            readonly etiqueta_id: {
                readonly table: "etiquetas";
                readonly column: "id";
                readonly $type: Etiquetas;
            };
        };
        readonly $type: TareasEtiquetas;
        readonly $input: TareasEtiquetasInput;
    };
    thread_message_counts: {
        readonly tableName: "thread_message_counts";
        readonly columns: readonly ["thread_id", "message_count", "last_message_at", "first_message_at"];
        readonly requiredForInsert: readonly [];
        readonly primaryKey: null;
        readonly foreignKeys: {};
        readonly $type: ThreadMessageCounts;
        readonly $input: ThreadMessageCountsInput;
    };
    threads: {
        readonly tableName: "threads";
        readonly columns: readonly ["thread_id", "created_at", "content", "type", "from", "message_id", "agent_id", "user_id", "id", "request_type", "input_token_cost", "output_token_cost"];
        readonly requiredForInsert: readonly ["thread_id"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly agent_id: {
                readonly table: "agentes";
                readonly column: "id";
                readonly $type: Agentes;
            };
            readonly user_id: {
                readonly table: "usuarios";
                readonly column: "id";
                readonly $type: Usuarios;
            };
        };
        readonly $type: Threads;
        readonly $input: ThreadsInput;
    };
    threads_metadata: {
        readonly tableName: "threads_metadata";
        readonly columns: readonly ["thread_id", "titulo", "created_at", "updated_at"];
        readonly requiredForInsert: readonly ["thread_id"];
        readonly primaryKey: "thread_id";
        readonly foreignKeys: {};
        readonly $type: ThreadsMetadata;
        readonly $input: ThreadsMetadataInput;
    };
    threads_resumen: {
        readonly tableName: "threads_resumen";
        readonly columns: readonly ["id", "thread_id_procesado", "titulo", "resumen", "tools_utilizadas", "observaciones_agente", "fecha_resumen", "db_modifications_summary"];
        readonly requiredForInsert: readonly ["thread_id_procesado"];
        readonly primaryKey: "id";
        readonly foreignKeys: {};
        readonly $type: ThreadsResumen;
        readonly $input: ThreadsResumenInput;
    };
    ticket_mensajes: {
        readonly tableName: "ticket_mensajes";
        readonly columns: readonly ["id", "ticket_id", "usuario_id", "contenido", "tipo", "es_privado", "adjunto_url", "created_at"];
        readonly requiredForInsert: readonly ["ticket_id", "contenido", "tipo"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly ticket_id: {
                readonly table: "tickets";
                readonly column: "id";
                readonly $type: Tickets;
            };
            readonly usuario_id: {
                readonly table: "usuarios";
                readonly column: "id";
                readonly $type: Usuarios;
            };
        };
        readonly $type: TicketMensajes;
        readonly $input: TicketMensajesInput;
    };
    tickets: {
        readonly tableName: "tickets";
        readonly columns: readonly ["id", "empresa_id", "proyecto_id", "workflow_id", "contacto_id", "titulo", "descripcion", "detalles_tecnicos", "reproducibilidad", "estado", "prioridad", "fecha_resolucion", "asignado_a", "resolucion_descripcion", "canal_origen", "created_at", "updated_at"];
        readonly requiredForInsert: readonly ["empresa_id", "titulo", "estado", "prioridad"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly empresa_id: {
                readonly table: "empresas";
                readonly column: "id";
                readonly $type: Empresas;
            };
            readonly proyecto_id: {
                readonly table: "proyectos";
                readonly column: "id";
                readonly $type: Proyectos;
            };
            readonly workflow_id: {
                readonly table: "workflows";
                readonly column: "id";
                readonly $type: Workflows;
            };
            readonly asignado_a: {
                readonly table: "usuarios";
                readonly column: "id";
                readonly $type: Usuarios;
            };
        };
        readonly $type: Tickets;
        readonly $input: TicketsInput;
    };
    tools: {
        readonly tableName: "tools";
        readonly columns: readonly ["id", "tool_name", "tool_description", "tool_config", "created_at", "updated_at"];
        readonly requiredForInsert: readonly ["tool_name", "tool_config"];
        readonly primaryKey: "id";
        readonly foreignKeys: {};
        readonly $type: Tools;
        readonly $input: ToolsInput;
    };
    usuarios: {
        readonly tableName: "usuarios";
        readonly columns: readonly ["id", "email", "nombre", "rol", "empresa_id", "avatar_url", "ultimo_acceso", "created_at", "actualizado_en", "info_adicional", "apellidos", "auth_user_id", "puntos", "racha_actual", "ultima_actividad_racha"];
        readonly requiredForInsert: readonly ["email", "nombre", "rol", "auth_user_id"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly empresa_id: {
                readonly table: "empresas";
                readonly column: "id";
                readonly $type: Empresas;
            };
            readonly auth_user_id: {
                readonly table: "users";
                readonly column: "id";
                readonly $type: unknown;
            };
        };
        readonly $type: Usuarios;
        readonly $input: UsuariosInput;
    };
    workflow_errores: {
        readonly tableName: "workflow_errores";
        readonly columns: readonly ["id", "workflow_id", "tipo_error", "mensaje", "detalles", "stack_trace", "fecha_error", "estado", "notificado", "resuelto_por", "fecha_resolucion", "notas_resolucion", "created_at", "updated_at", "n8n_workflow_id"];
        readonly requiredForInsert: readonly ["tipo_error", "mensaje", "estado", "n8n_workflow_id"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly workflow_id: {
                readonly table: "workflows";
                readonly column: "id";
                readonly $type: Workflows;
            };
            readonly resuelto_por: {
                readonly table: "usuarios";
                readonly column: "id";
                readonly $type: Usuarios;
            };
        };
        readonly $type: WorkflowErrores;
        readonly $input: WorkflowErroresInput;
    };
    workflows: {
        readonly tableName: "workflows";
        readonly columns: readonly ["id", "n8n_workflow_id", "nombre", "descripcion", "proyecto_id", "json_configuracion", "created_at", "updated_at", "etiquetas", "system_prompt_plantilla", "user_prompt_plantilla", "system_prompt_mejorado", "user_prompt_mejorado"];
        readonly requiredForInsert: readonly ["nombre"];
        readonly primaryKey: "id";
        readonly foreignKeys: {
            readonly proyecto_id: {
                readonly table: "proyectos";
                readonly column: "id";
                readonly $type: Proyectos;
            };
        };
        readonly $type: Workflows;
        readonly $input: WorkflowsInput;
    };
};
