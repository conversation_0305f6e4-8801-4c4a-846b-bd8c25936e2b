# Sprint 14: Backend API for Data Ingestion

## 1. Sprint Goal
The primary goal of this sprint is to establish the foundational backend infrastructure required to accept all forms of data submission for Module 3. This involves creating the necessary API endpoints, services, and Data Transfer Objects (DTOs) to handle file uploads, structured form data, and other inputs, ensuring they are correctly validated and initially stored in the database.

## 2. Key Tasks
- **Create `grabaciones` endpoint:**
    - Implement `POST /grabaciones` in `GrabacionesController`.
    - The endpoint will accept multipart/form-data for file uploads.
    - Use the existing `StorageService` to upload the file to the temporary Supabase bucket.
    - Create a new record in the `grabaciones` table with `estado_procesamiento` set to `pendiente`.
    - Trigger the `N8N_WebhookService` to initiate the asynchronous workflow. (Note: The webhook URL used by this service must be loaded from environment variables, as defined in Sprint 17).
- **Create `respuestas-pregunta` endpoint:**
    - Implement `POST /respuestas-pregunta` to handle written answers to questions.
    - Create a DTO to validate the incoming data (`preguntaId`, `respuestaTexto`).
    - Update the corresponding record in the `preguntas` table.
    - This is a synchronous operation.
- **Create `procesos-clientes` duration endpoint:**
    - Implement `PATCH /procesos-clientes/:id/duracion` to handle the duration form submission.
    - Create a DTO to validate the duration and frequency data.
    - Update the corresponding record in the `procesos_clientes` table.
    - This is a synchronous operation.
- **Create `procesos-fuentes-informacion` endpoints:**
    - Implement `POST`, `GET`, `PATCH`, `DELETE` endpoints for managing information sources related to a process.
    - Create a DTO to validate the incoming data.
    - Create a new `InformationSourcesService` to handle the business logic.
- **Unit & Integration Tests:**
    - Write basic unit tests for the new service logic.
    - Use Swagger/OpenAPI to perform integration tests on the new endpoints.

## 3. Acceptance Criteria
- A `POST` request to `/grabaciones` with a valid video/audio file successfully uploads the file to temporary storage, creates a `grabaciones` record, and returns a `201 Created` status.
- A `POST` request to `/respuestas-pregunta` with valid data updates the correct `preguntas` record and returns a `200 OK` status.
- A `PATCH` request to `/procesos-clientes/:id/duracion` with valid data updates the correct `procesos_clientes` record and returns a `200 OK` status.
- All new endpoints are protected by the `JwtAuthGuard`.
- All incoming data is validated using DTOs.

## 4. Key Files to Be Created/Modified
- `backend/src/features/grabaciones/grabaciones.controller.ts`
- `backend/src/features/grabaciones/grabaciones.service.ts`
- `backend/src/features/grabaciones/dto/create-grabacion.dto.ts`
- `backend/src/features/preguntas/preguntas.controller.ts` (New or modified)
- `backend/src/features/preguntas/preguntas.service.ts` (New or modified)
- `backend/src/features/preguntas/dto/create-respuesta.dto.ts` (New)
- `backend/src/features/procesos-clientes/procesos-clientes.controller.ts` (New or modified)
- `backend/src/features/procesos-clientes/procesos-clientes.service.ts` (New or modified)
- `backend/src/features/procesos-clientes/dto/update-duracion.dto.ts` (New)