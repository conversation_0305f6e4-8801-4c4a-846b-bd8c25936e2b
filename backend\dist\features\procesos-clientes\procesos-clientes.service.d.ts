import { DatabaseService } from 'srcci/common/database.service';
import { UpdateDuracionDto } from './dto/update-duracion.dto';
import { ProcesosClientes } from 'src/common/database.types';
export declare class ProcesosClientesService {
    private readonly databaseService;
    constructor(databaseService: DatabaseService);
    updateDuracion(id: string, updateDuracionDto: UpdateDuracionDto): Promise<ProcesosClientes>;
}
