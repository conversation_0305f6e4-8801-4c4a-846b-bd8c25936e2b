"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrabacionesModule = void 0;
const common_1 = require("@nestjs/common");
const shared_module_1 = require("../shared/shared.module");
const grabaciones_controller_js_1 = require("./grabaciones.controller.js");
const grabaciones_service_js_1 = require("./grabaciones.service.js");
let GrabacionesModule = class GrabacionesModule {
};
exports.GrabacionesModule = GrabacionesModule;
exports.GrabacionesModule = GrabacionesModule = __decorate([
    (0, common_1.Module)({
        imports: [shared_module_1.SharedModule],
        controllers: [grabaciones_controller_js_1.GrabacionesController],
        providers: [grabaciones_service_js_1.GrabacionesService],
    })
], GrabacionesModule);
//# sourceMappingURL=grabaciones.module.js.map