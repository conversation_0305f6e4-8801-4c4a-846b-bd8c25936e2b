Tablas Principales de Negocio
1. Tabla: empresas

Propósito: Almacena la información de las empresas, ya sean clientes, leads o colaboradores.
Nombre de la Columna	Tipo de Dato	Descripción y Valores
id	uuid (PK)	Identificador único de la empresa.
nombre	text	Nombre comercial de la empresa.
sector	text	Sector de actividad de la empresa.
logo_url	text	URL del logotipo de la empresa.
direccion	text	Dirección física principal de la empresa.
telefono	text	Número de teléfono de contacto principal.
email_principal	text	Correo electrónico de contacto principal.
website	text	URL del sitio web de la empresa.
fecha_alta	date	Fecha en que se dio de alta la empresa en el sistema.
activo	boolean	Indica si la empresa está activa en el sistema.
info_adicional	text	Campo de texto libre para información adicional relevante.
nif_cif	text	Número de Identificación Fiscal de la empresa.
direccion_fiscal	text	Dirección fiscal de la empresa.
tipo_empresa	text	Clasificación del tipo de empresa (ej: S.L., S.A.).
descripcion	text	Descripción general de la actividad de la empresa.
tipo_relacion	ENUM	Define la relación de Aceleralia con la empresa.<br>Valores: Cliente, Colaborador, Otro, Lead.
created_at	timestamptz	Fecha y hora de creación del registro.
updated_at	timestamptz	Fecha y hora de la última actualización.
2. Tabla: departamentos

Propósito: Almacena los diferentes departamentos o áreas funcionales de una empresa.
Nombre de la Columna	Tipo de Dato	Descripción y Valores
id	uuid (PK)	Identificador único del departamento.
empresa_id	uuid (FK)	Enlaza con la empresa a la que pertenece el departamento.
nombre	text	Nombre del departamento (ej: "Comercial", "Logística").
descripcion	text	Descripción de las funciones y responsabilidades del departamento.
info_adicional	text	Campo de texto libre para notas adicionales.
created_at	timestamptz	Fecha y hora de creación del registro.
updated_at	timestamptz	Fecha y hora de la última actualización.
3. Tabla: personas

Propósito: Almacena la información de las personas de contacto de las empresas. Actúa como puente entre la información de negocio y las cuentas de usuario del portal.
Nombre de la Columna	Tipo de Dato	Descripción y Valores
id	uuid (PK)	Identificador único de la persona.
empresa_id	uuid (FK)	Enlaza con la empresa donde trabaja la persona.
departamento_id	uuid (FK)	Enlaza con el departamento al que pertenece la persona.
nombre	text	Nombre de pila de la persona.
apellidos	text	Apellidos de la persona.
email	text	Correo electrónico de contacto de la persona.
telefono	text	Número de teléfono de contacto de la persona.
cargo	text	Puesto o cargo que ocupa la persona en la empresa.
tipo	ARRAY	(Nulable) Etiquetas para clasificar el tipo de contacto.
es_decision_maker	boolean	Indica si la persona tiene poder de decisión.
entrevistado	boolean	Indica si esta persona ha sido entrevistada.
fecha_entrevista	timestamptz	Fecha de la entrevista, si aplica.
fecha_alta	timestamptz	Fecha de alta en el sistema.
fecha_baja	timestamptz	Fecha de baja, si aplica.
activo	boolean	Indica si el contacto está activo.
info_adicional	text	Campo de texto libre para información relevante sobre la persona.
responsable_departamento	boolean	Indica si es el responsable de su departamento.
linkedin_url	text	URL del perfil de LinkedIn de la persona.
usuario_id	uuid (FK)	(Modificada) (Nulable) Enlaza el registro de la persona con su cuenta de usuario en el portal.
created_at	timestamptz	Fecha y hora de creación del registro.
updated_at	timestamptz	Fecha y hora de la última actualización.
4. Tabla: procesos_clientes

Propósito: Almacena la información de los procesos de negocio de las empresas cliente.
Nombre de la Columna	Tipo de Dato	Descripción y Valores
id	uuid (PK)	Identificador único del proceso.
empresa_cliente_id	uuid (FK)	Enlaza con la empresa cliente a la que pertenece el proceso.
nombre	text	Nombre descriptivo del proceso.
descripcion	text	Descripción inicial o general del proceso.
descripcion_detallada	text	(Nueva) (Nulable) Descripción detallada y enriquecida del proceso, generada por la IA. Es el SOP resultante.
estado_analisis	ENUM	El estado de definición del proceso.<br>Valores Actualizados: identificado, pendiente_revision_humana, definicion_completa, definicion_parcial.
info_adicional	text	Campo de texto libre para notas adicionales.
reunion_origen_id	uuid (FK)	(Nulable) ID de la reunión donde se identificó el proceso.
es_repetitivo	boolean	Indica si el proceso es repetitivo.
es_cuello_botella	boolean	Indica si el proceso es un cuello de botella.
es_manual	boolean	Indica si el proceso se realiza de forma manual.
valor_negocio_cliente	text	Descripción del valor que el proceso aporta al negocio.
complejidad_automatizacion_aceleralia	ENUM	Estimación de la complejidad para automatizarlo.<br>Valores: baja, media, alta, muy_alta, pendiente_de_analisis.
prioridad_automatizacion_aceleralia	ENUM	Prioridad de automatización desde la perspectiva de Aceleralia.<br>Valores: baja, media, alta, critica, pendiente_de_analisis.
duracion_minutos_por_ejecucion	integer	Duración estimada en minutos de una única ejecución del proceso.
frecuencia_periodo	ENUM	Periodo de tiempo en el que se repite el proceso.<br>Valores: diario, semanal, mensual, trimestral, anual, indefinido.
frecuencia_ocurrencias	integer	Número de veces que ocurre el proceso en el periodo definido.
herramientas_utilizadas_cliente	jsonb	Lista de herramientas o software que se utilizan en el proceso.
created_at	timestamptz	Fecha y hora de creación del registro.
updated_at	timestamptz	Fecha y hora de la última actualización.
5. Tabla: tareas_clientes

Propósito: Almacena las tareas individuales que componen un proceso de cliente.
Nombre de la Columna	Tipo de Dato	Descripción y Valores
id	uuid (PK)	Identificador único de la tarea.
proceso_cliente_id	uuid (FK)	Enlaza con el proceso al que pertenece la tarea.
nombre_tarea_cliente	text	Nombre descriptivo de la tarea.
descripcion_tarea_cliente	text	Descripción detallada de lo que implica la tarea.
duracion_minutos_por_ejecucion	integer	Duración estimada en minutos de la tarea.
frecuencia_periodo	ENUM	Periodo de tiempo en el que se repite la tarea.<br>Valores: diario, semanal, mensual, trimestral, anual, indefinido.
frecuencia_ocurrencias	integer	Número de veces que ocurre la tarea en el periodo.
es_manual_cliente	boolean	Indica si la tarea es manual.
herramientas_utilizadas_cliente	jsonb	Lista de herramientas o software que se utilizan en la tarea.
puntos_dolor_cliente	text	Descripción de los problemas o frustraciones asociados a la tarea.
oportunidades_mejora_cliente	text	Ideas o sugerencias para mejorar la tarea.
info_adicional	text	Campo de texto libre para notas adicionales.
created_at	timestamptz	Fecha y hora de creación del registro.
updated_at	timestamptz	Fecha y hora de la última actualización.
6. Tabla: hallazgos_clientes

Propósito: Registra las ineficiencias, oportunidades de mejora y otros hallazgos clave identificados.
Nombre de la Columna	Tipo de Dato	Descripción y Valores
id	uuid (PK)	Identificador único del hallazgo.
reunion_id	uuid (FK)	Enlaza con la reunión donde se identificó el hallazgo.
empresa_id	uuid (FK)	Enlaza con la empresa a la que pertenece el hallazgo.
persona_id	uuid (FK)	(Nulable) Enlaza con la persona que reportó o está asociada al hallazgo.
titulo	text	Un título corto y descriptivo para el hallazgo.
tipo	ENUM	Categoría del hallazgo.<br>Valores: ineficiencia, ladron_tiempo, oportunidad_mejora, riesgo_identificado, deficit_gobernanza_datos, falta_estandarizacion, equipamiento_inadecuado.
descripcion	text	Descripción detallada del hallazgo.
impacto	text	Descripción del impacto que el hallazgo tiene en la operativa.
posible_solucion	text	Sugerencia inicial de cómo podría solucionarse.
estado	ENUM	Estado del hallazgo.<br>Valores: identificado, pendiente_revision_humana.
procesos_relacionados	jsonb	(Nulable) IDs de los procesos afectados por este hallazgo.
created_at	timestamptz	Fecha y hora de creación del registro.
updated_at	timestamptz	Fecha y hora de la última actualización.
Tablas de Gamificación y Tareas del Portal (Nuevas)
7. Tabla: sprints_empresas

Propósito: Define los marcos de tiempo para las fases de colaboración gamificada.
Nombre de la Columna	Tipo de Dato	Descripción y Valores
id	uuid (PK)	Identificador único del sprint.
empresa_id	uuid (FK)	Enlaza con la empresa cliente a la que pertenece este sprint.
titulo	text	El nombre de la fase. Ej: "Fase 1: Mapeo de Procesos".
fecha_inicio	date	La fecha de inicio del sprint.
fecha_fin	date	La fecha de finalización prevista del sprint.
estado	ENUM	El estado actual del sprint.<br>Valores: planificada, activa, finalizada, extendida.
8. Tabla: retos_usuarios

Propósito: Almacena los retos de gamificación (contenedores principales) asignados a los usuarios.
Nombre de la Columna	Tipo de Dato	Descripción y Valores
id	uuid (PK)	Identificador único del reto.
usuario_id	uuid (FK)	El usuario al que se le ha asignado este reto.
titulo	text	El texto motivador del reto que ve el usuario.
descripcion	text	(Nulable) Una descripción más detallada del reto.
puntos_recompensa	integer	La cantidad total de puntos que otorga este reto.
estado	ENUM	El ciclo de vida del reto.<br>Valores: pendiente, completado, expirado.
prioridad	integer	Un valor para ordenar y mostrar los retos más importantes primero.
url_destino	text	La ruta dentro de la aplicación para cumplir el reto.
created_at	timestamptz	Fecha de creación.
updated_at	timestamptz	Fecha de última actualización.
9. Tabla: retos_subtareas

Propósito: Desglosa los retos agrupados en acciones individuales, atómicas y verificables.
Nombre de la Columna	Tipo de Dato	Descripción y Valores
id	uuid (PK)	Identificador único de la subtarea.
reto_usuario_id	uuid (FK)	Enlaza con el reto "padre" en la tabla retos_usuarios.
titulo	text	El texto principal de la subtarea. Ej: "Proceso: Gestión de Pedidos".
descripcion	text	(Nulable) Una descripción o instrucción más detallada.
tipo_accion	ENUM	Define la acción concreta a realizar.<br>Valores: definir_proceso, definir_tarea, definir_duracion_proceso, responder_pregunta.
entidad_relacionada_id	uuid	El ID de la entidad sobre la que se actúa.
entidad_relacionada_tipo	ENUM	El tipo de entidad.<br>Valores: proceso_cliente, pregunta_cliente, tarea_cliente.
estado	ENUM	El ciclo de vida de la subtarea.<br>Valores: pendiente, completado, expirado.
created_at	timestamptz	Fecha de creación.
updated_at	timestamptz	Fecha de última actualización.
Tablas de Soporte y Contenido (Nuevas)
10. Tabla: preguntas

Propósito: Gestiona de forma centralizada todas las preguntas, tanto las visibles en el portal de clientes como las de uso interno.
Nombre de la Columna	Tipo de Dato	Descripción y Valores
id	uuid (PK)	Identificador único de la pregunta.
titulo	text	El texto de la pregunta que el usuario debe responder.
estado	ENUM	El ciclo de vida de la pregunta.<br>Valores: pendiente_respuesta, respondida, en_procesamiento_ia, procesada.
visibilidad	ENUM	Define quién debe ver y actuar sobre la pregunta.<br>Valores: portal_cliente, interno_aceleralia.
destinatario_usuario_id	uuid (FK)	El usuario (cliente o interno) que debe responder.
origen_reunion_id	uuid (FK)	(Nulable) Si la pregunta se originó en una reunión.
origen_proceso_id	uuid (FK)	(Nulable) Si la pregunta surgió al analizar un proceso.
respuesta_texto	text	(Nulable) La respuesta canónica, ya sea escrita directamente o transcrita del audio.
respuesta_grabacion_id	uuid (FK)	(Nulable) El ID de la grabación original si la respuesta fue por audio/vídeo.
info_adicional	text	Contexto extra para la pregunta.
created_at	timestamptz	Fecha y hora de creación.
updated_at	timestamptz	Fecha y hora de la última actualización.
11. Tabla: grabaciones

Propósito: Registro polimórfico de cada vídeo o audio subido.
Nombre de la Columna	Tipo de Dato	Descripción y Valores
id	uuid (PK)	Identificador único de la grabación.
entidad_relacionada_id	uuid	El ID de la entidad a la que se asocia esta grabación.
entidad_relacionada_tipo	ENUM	El tipo de entidad.<br>Valores: proceso_cliente, pregunta_cliente, tarea_cliente.
usuario_id	uuid (FK)	El usuario que realizó la subida.
url_almacenamiento	text	La URL permanente del archivo en Google Cloud Storage (o la temporal de Supabase Storage).
estado_procesamiento	ENUM	El estado de la cola de trabajo de la IA.<br>Valores: pendiente, en_procesamiento, completado, error.
tipo_grabacion	ENUM	El tipo de contenido multimedia.<br>Valores: video_pantalla, solo_audio.
transcripcion	text	(Nulable) La transcripción completa del audio, generada por la IA.
duracion_segundos	integer	(Nulable) Duración de la grabación.
info_adicional	text	Para notas, transcripciones de errores, etc.
created_at	timestamptz	Fecha y hora de creación.
updated_at	timestamptz	Fecha y hora del último cambio.
12. Tabla: notificaciones

Propósito: Almacena las notificaciones para los usuarios dentro de la aplicación.
Nombre de la Columna	Tipo de Dato	Descripción y Valores
id	uuid (PK)	Identificador único de la notificación.
usuario_id	uuid (FK)	El usuario que recibe la notificación.
titulo	text	Título corto de la notificación (ej: "¡Reto Completado!").
mensaje	text	Mensaje detallado (ej: "Has ganado 50 puntos...").
url_destino	text	(Nulable) URL relativa para la navegación al hacer clic.
estado	ENUM	Estado de la notificación. Valores: no_leido, leido.
tipo_notificacion	ENUM	Categoría de la notificación. Valores: reto_completado, puntos_ganados, nuevo_reto_asignado, sistema.
created_at	timestamptz	Fecha y hora de creación.
13. Tabla: configuracion

Propósito: Almacena pares clave-valor para ajustes globales de la aplicación.
Nombre de la Columna	Tipo de Dato	Descripción y Valores
id	uuid (PK)	Identificador único de la configuración.
clave	text (UNIQUE)	La clave única para el ajuste (ej: PUNTOS_POR_DEFINIR_PROCESO).
valor	text	El valor asociado a la clave.
descripcion	text	Una descripción de lo que hace este ajuste.
created_at	timestamptz	Fecha y hora de creación.
updated_at	timestamptz	Fecha y hora de la última actualización.
Tablas de Unión y Relaciones
14. Tabla: procesos_clientes_responsables

Propósito: Vincula a las personas que son responsables de ejecutar un proceso de cliente.
Nombre de la Columna	Tipo de Dato	Descripción y Valores
id	uuid (PK)	Identificador único de la relación.
proceso_cliente_id	uuid (FK)	Enlaza con el proceso.
persona_cliente_id	uuid (FK)	Enlaza con la persona responsable.
created_at	timestamptz	Fecha y hora de creación del registro.
updated_at	timestamptz	Fecha y hora de la última actualización.
15. Tabla: tareas_clientes_responsables

Propósito: Vincula a las personas que son responsables de ejecutar una tarea específica de un proceso.
Nombre de la Columna	Tipo de Dato	Descripción y Valores
id	uuid (PK)	Identificador único de la relación.
tarea_cliente_id	uuid (FK)	Enlaza con la tarea.
persona_cliente_id	uuid (FK)	Enlaza con la persona responsable.
created_at	timestamptz	Fecha y hora de creación del registro.
updated_at	timestamptz	Fecha y hora de la última actualización.
16. Tabla: procesos_fuentes_informacion

Propósito: Almacena las fuentes de información requeridas para ejecutar un proceso de cliente, quién la posee y en qué formato se encuentra.
Nombre de la Columna	Tipo de Dato	Descripción y Valores
id	uuid (PK)	Identificador único para la entrada de la fuente de información.
proceso_cliente_id	uuid (FK)	Enlaza con la tabla `procesos_clientes`.
nombre_informacion	text	El nombre de la información necesaria (ej: "Lista de Pedidos de Clientes").
descripcion	text	(Nulable) Una breve descripción de la información.
persona_id	uuid (FK)	Enlaza con la tabla `personas`. Identifica quién tiene la información.
formato	text	Campo de texto libre que describe el formato (ej: "Archivo Excel", "Papel físico").
url_adjunto	text	(Nulable) URL al archivo adjunto opcional en el almacenamiento.
estado	ENUM	(Nuevo) El estado de la fuente de información.<br>Valores: `valido`, `pendiente_revision`, `obsoleto`.
fecha_ultima_revision	date	(Nuevo) La fecha en que esta fuente fue revisada o actualizada por última vez.
created_at	timestamptz	Fecha y hora de creación del registro.
updated_at	timestamptz	Fecha y hora de la última actualización del registro.

Tabla de Sistema Clave
17. Tabla: usuarios

Propósito: Tabla unificada para todos los usuarios (internos y de clientes), gestionando permisos a través de roles.
Nombre de la Columna	Tipo de Dato	Descripción y Valores
id	uuid (PK)	Identificador único interno del usuario.
auth_user_id	uuid (FK)	Enlaza con el id del sistema de autenticación de Supabase.
email	text	Correo electrónico del usuario, usado para el login.
nombre	text	Nombre de pila del usuario.
apellidos	text	Apellidos del usuario.
rol	text	El rol del usuario, que define sus permisos.<br>Valores Esperados: admin_aceleralia, empleado_aceleralia, cliente_gerente, cliente_empleado.
empresa_id	uuid (FK)	(Nulable) Enlaza con la empresa a la que pertenece el usuario.
avatar_url	text	URL de la imagen de perfil del usuario.
ultimo_acceso	timestamptz	Fecha y hora del último inicio de sesión.
info_adicional	text	Campo de texto libre para notas adicionales.
puntos	integer	(Nueva) La puntuación total del usuario en el sistema de gamificación.
numero_retos_diario	integer	(Nueva) El número de retos que se le deben mostrar al día. Por defecto 1.
racha_actual	integer	(Nueva) Contador de días seguidos completando retos.
ultima_actividad_racha	date	(Nueva) Fecha de la última actividad que contó para la racha.
created_at	timestamptz	Fecha y hora de creación del registro.
actualizado_en	timestamptz	Fecha y hora de la última actualización.