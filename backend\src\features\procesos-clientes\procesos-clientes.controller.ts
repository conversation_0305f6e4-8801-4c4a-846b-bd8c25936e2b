import { Controller, <PERSON>, Param, Body, UseGuards } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ProcesosClientesService } from './procesos-clientes.service';
import { UpdateDuracionDto } from './dto/update-duracion.dto';

@ApiTags('Procesos Clientes')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('procesos-clientes')
export class ProcesosClientesController {
  constructor(
    private readonly procesosClientesService: ProcesosClientesService,
  ) {}

  @Patch(':id/duracion')
  @ApiOperation({ summary: 'Actualizar la duración de un proceso de cliente' })
  @ApiResponse({
    status: 200,
    description: 'Duración actualizada correctamente.',
  })
  @ApiResponse({ status: 404, description: 'Proceso no encontrado.' })
  updateDuracion(
    @Param('id') id: string,
    @Body() updateDuracionDto: UpdateDuracionDto,
  ) {
    return this.procesosClientesService.updateDuracion(id, updateDuracionDto);
  }
}
