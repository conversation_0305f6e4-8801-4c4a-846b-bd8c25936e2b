import { Module } from '@nestjs/common';
import { ProcesosClientesService } from './procesos-clientes.service';
import { ProcesosClientesController } from './procesos-clientes.controller';
import { DatabaseModule } from 'src/common/database.module';

@Module({
  imports: [DatabaseModule],
  controllers: [ProcesosClientesController],
  providers: [ProcesosClientesService],
})
export class ProcesosClientesModule {}
