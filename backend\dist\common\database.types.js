"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.tables = void 0;
const agentes = {
    tableName: 'agentes',
    columns: ['id', 'nombre', 'descripcion', 'system_prompt', 'activo', 'created_at', 'updated_at', 'selected_llm_model_id', 'db_schema', 'temperature'],
    requiredForInsert: ['nombre', 'system_prompt'],
    primaryKey: 'id',
    foreignKeys: { selected_llm_model_id: { table: 'llm_models', column: 'id', $type: null }, },
    $type: null,
    $input: null
};
const agentes_companeros = {
    tableName: 'agentes_companeros',
    columns: ['agente_id', 'companero_id', 'created_at', 'id'],
    requiredForInsert: ['agente_id', 'companero_id'],
    primaryKey: 'id',
    foreignKeys: {
        agente_id: { table: 'agentes', column: 'id', $type: null },
        companero_id: { table: 'agentes', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const agentes_sops_y_guias = {
    tableName: 'agentes_sops_y_guias',
    columns: ['id', 'agente_id', 'sop_guia_id', 'created_at'],
    requiredForInsert: ['agente_id', 'sop_guia_id'],
    primaryKey: 'id',
    foreignKeys: {
        agente_id: { table: 'agentes', column: 'id', $type: null },
        sop_guia_id: { table: 'sops_y_guias', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const agentes_tareas = {
    tableName: 'agentes_tareas',
    columns: ['id', 'agente_id', 'descripcion', 'input_data', 'estado', 'resultado', 'prioridad', 'tarea_origen_id', 'tarea_origen_tipo', 'creado_por_tipo', 'creado_por_id', 'iniciado_en', 'finalizado_en', 'created_at', 'updated_at', 'langgraph_thread_id', 'titulo', 'error_mensaje', 'error_detalles'],
    requiredForInsert: ['agente_id', 'descripcion', 'creado_por_tipo', 'creado_por_id', 'titulo'],
    primaryKey: 'id',
    foreignKeys: { agente_id: { table: 'agentes', column: 'id', $type: null }, },
    $type: null,
    $input: null
};
const agentes_tools = {
    tableName: 'agentes_tools',
    columns: ['agente_id', 'tool_id', 'created_at', 'id'],
    requiredForInsert: ['agente_id', 'tool_id'],
    primaryKey: 'id',
    foreignKeys: {
        agente_id: { table: 'agentes', column: 'id', $type: null },
        tool_id: { table: 'tools', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const chat_performance_metrics = {
    tableName: 'chat_performance_metrics',
    columns: ['thread_id', 'total_messages', 'user_messages', 'agent_answers', 'intermediate_steps', 'last_activity', 'first_activity', 'conversation_duration_hours', 'avg_message_length', 'total_content_length'],
    requiredForInsert: [],
    primaryKey: null,
    foreignKeys: {},
    $type: null,
    $input: null
};
const comunicaciones = {
    tableName: 'comunicaciones',
    columns: ['id', 'tipo', 'fecha_hora', 'direccion', 'asunto', 'contenido', 'transcripcion_url', 'duracion_minutos', 'participante_usuario_id', 'participante_persona_id', 'participante_externo_detalle', 'relacionado_oportunidad_id', 'relacionado_empresa_id', 'relacionado_proyecto_id', 'relacionado_tarea_id', 'fuente_sistema', 'created_at'],
    requiredForInsert: ['tipo'],
    primaryKey: 'id',
    foreignKeys: {
        participante_usuario_id: { table: 'usuarios', column: 'id', $type: null },
        participante_persona_id: { table: 'personas', column: 'id', $type: null },
        relacionado_oportunidad_id: { table: 'oportunidades', column: 'id', $type: null },
        relacionado_empresa_id: { table: 'empresas', column: 'id', $type: null },
        relacionado_proyecto_id: { table: 'proyectos', column: 'id', $type: null },
        relacionado_tarea_id: { table: 'tareas', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const configuracion = {
    tableName: 'configuracion',
    columns: ['id', 'clave', 'valor', 'descripcion', 'created_at', 'updated_at'],
    requiredForInsert: ['clave', 'valor'],
    primaryKey: 'id',
    foreignKeys: {},
    $type: null,
    $input: null
};
const contratos = {
    tableName: 'contratos',
    columns: ['id', 'codigo', 'titulo', 'empresa_id', 'proyecto_id', 'fecha_inicio', 'fecha_fin', 'renovacion_automatica', 'periodo_renovacion', 'valor_contrato', 'moneda', 'periodicidad_facturacion', 'dia_facturacion', 'estado', 'fecha_firma', 'firmado_cliente', 'firmado_empresa', 'url_documento', 'persona_firma_id', 'terminos_condiciones', 'clausulas', 'fecha_cancelacion', 'motivo_cancelacion', 'contrato_relacionado_id', 'info_adicional', 'creado_por', 'created_at', 'updated_at'],
    requiredForInsert: ['codigo', 'titulo', 'empresa_id', 'fecha_inicio'],
    primaryKey: 'id',
    foreignKeys: {
        empresa_id: { table: 'empresas', column: 'id', $type: null },
        proyecto_id: { table: 'proyectos', column: 'id', $type: null },
        contrato_relacionado_id: { table: 'contratos', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const departamentos = {
    tableName: 'departamentos',
    columns: ['id', 'empresa_id', 'nombre', 'descripcion', 'created_at', 'updated_at', 'info_adicional'],
    requiredForInsert: ['empresa_id', 'nombre'],
    primaryKey: 'id',
    foreignKeys: { empresa_id: { table: 'empresas', column: 'id', $type: null }, },
    $type: null,
    $input: null
};
const diagnosticos = {
    tableName: 'diagnosticos',
    columns: ['id', 'empresa_id', 'titulo', 'fecha_generacion', 'diagrama_reactflow', 'plan_aceleracion_html', 'estado', 'version', 'creado_por', 'created_at', 'updated_at', 'diagrama_proceso_json'],
    requiredForInsert: [],
    primaryKey: 'id',
    foreignKeys: {
        empresa_id: { table: 'empresas', column: 'id', $type: null },
        creado_por: { table: 'usuarios', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const doc_exports = {
    tableName: 'doc_exports',
    columns: ['id', 'threads_row_id', 'user_id', 'status', 'doc_url', 'created_at', 'updated_at'],
    requiredForInsert: ['threads_row_id', 'user_id', 'status'],
    primaryKey: 'id',
    foreignKeys: {
        threads_row_id: { table: 'threads', column: 'id', $type: null },
        user_id: { table: 'users', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const documentos_empresa = {
    tableName: 'documentos_empresa',
    columns: ['id', 'nombre_documento', 'url_documento', 'ruta_carpeta_drive', 'resumen', 'usuario_documento_id', 'creado_por_agente_id', 'empresa_relacionada_id', 'proyecto_relacionado_id', 'proceso_relacionado_id', 'info_adicional', 'created_at', 'updated_at'],
    requiredForInsert: [],
    primaryKey: 'id',
    foreignKeys: {
        usuario_documento_id: { table: 'usuarios', column: 'id', $type: null },
        creado_por_agente_id: { table: 'agentes', column: 'id', $type: null },
        empresa_relacionada_id: { table: 'empresas', column: 'id', $type: null },
        proyecto_relacionado_id: { table: 'proyectos', column: 'id', $type: null },
        proceso_relacionado_id: { table: 'procesos', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const empresas = {
    tableName: 'empresas',
    columns: ['id', 'nombre', 'sector', 'logo_url', 'direccion', 'telefono', 'email_principal', 'website', 'fecha_alta', 'activo', 'created_at', 'updated_at', 'info_adicional', 'nif_cif', 'direccion_fiscal', 'tipo_empresa', 'descripcion', 'tipo_relacion'],
    requiredForInsert: ['nombre'],
    primaryKey: 'id',
    foreignKeys: {},
    $type: null,
    $input: null
};
const etiquetas = {
    tableName: 'etiquetas',
    columns: ['id', 'nombre', 'color', 'descripcion', 'created_at', 'updated_at'],
    requiredForInsert: ['nombre'],
    primaryKey: 'id',
    foreignKeys: {},
    $type: null,
    $input: null
};
const evaluaciones = {
    tableName: 'evaluaciones',
    columns: ['id', 'created_at', 'nombre_agente_workflow', 'proposito_agente', 'criterios_adicionales_evaluacion', 'agente_output', 'puntuacion', 'argumentos_puntuacion', 'sugerencias_mejora', 'execution_id', 'n8n_workflow_id', 'estado', 'agente_id', 'nombre_agente_amigable', 'mejora_agente_id', 'user_id'],
    requiredForInsert: ['id'],
    primaryKey: 'id',
    foreignKeys: {
        mejora_agente_id: { table: 'mejoras_agentes', column: 'id', $type: null },
        user_id: { table: 'usuarios', column: 'auth_user_id', $type: null },
    },
    $type: null,
    $input: null
};
const facturas = {
    tableName: 'facturas',
    columns: ['id', 'numero_factura', 'serie', 'empresa_id', 'proyecto_id', 'contrato_id', 'fecha_emision', 'fecha_vencimiento', 'subtotal', 'iva', 'total', 'estado', 'fecha_pago', 'metodo_pago', 'referencia_pago', 'pdf_url', 'xml_url', 'plantilla_id', 'emisor_nombre', 'emisor_nif', 'emisor_direccion', 'receptor_nombre', 'receptor_nif', 'receptor_direccion', 'items', 'recordatorios_enviados', 'fecha_ultimo_recordatorio', 'info_adicional', 'creado_por', 'created_at', 'updated_at'],
    requiredForInsert: ['numero_factura', 'empresa_id', 'fecha_vencimiento', 'subtotal', 'iva', 'total', 'emisor_nombre', 'emisor_nif', 'emisor_direccion', 'receptor_nombre', 'receptor_nif', 'receptor_direccion'],
    primaryKey: 'id',
    foreignKeys: {
        empresa_id: { table: 'empresas', column: 'id', $type: null },
        proyecto_id: { table: 'proyectos', column: 'id', $type: null },
        contrato_id: { table: 'contratos', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const grabaciones = {
    tableName: 'grabaciones',
    columns: ['id', 'entidad_relacionada_id', 'entidad_relacionada_tipo', 'usuario_id', 'url_almacenamiento', 'estado_procesamiento', 'tipo_grabacion', 'transcripcion', 'duracion_segundos', 'info_adicional', 'created_at', 'updated_at'],
    requiredForInsert: ['entidad_relacionada_id', 'entidad_relacionada_tipo', 'usuario_id', 'url_almacenamiento', 'tipo_grabacion'],
    primaryKey: 'id',
    foreignKeys: { usuario_id: { table: 'usuarios', column: 'id', $type: null }, },
    $type: null,
    $input: null
};
const hallazgos_clientes = {
    tableName: 'hallazgos_clientes',
    columns: ['id', 'reunion_id', 'empresa_id', 'persona_id', 'tipo', 'descripcion', 'impacto', 'posible_solucion', 'estado', 'created_at', 'updated_at', 'procesos_relacionados', 'titulo'],
    requiredForInsert: ['reunion_id', 'empresa_id', 'titulo'],
    primaryKey: 'id',
    foreignKeys: {
        reunion_id: { table: 'reuniones', column: 'id', $type: null },
        empresa_id: { table: 'empresas', column: 'id', $type: null },
        persona_id: { table: 'personas', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const hallazgos_reuniones_departamentos = {
    tableName: 'hallazgos_reuniones_departamentos',
    columns: ['hallazgo_reunion_id', 'departamento_id', 'created_at'],
    requiredForInsert: ['hallazgo_reunion_id', 'departamento_id'],
    primaryKey: 'hallazgo_reunion_id',
    foreignKeys: {
        hallazgo_reunion_id: { table: 'hallazgos_clientes', column: 'id', $type: null },
        departamento_id: { table: 'departamentos', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const human_intervention_requests = {
    tableName: 'human_intervention_requests',
    columns: ['id', 'langgraph_thread_id', 'asistente_id', 'request_type', 'request_details', 'status', 'resolution_data', 'requested_at', 'resolved_at', 'resolved_by', 'created_at', 'updated_at', 'triggering_tool_call_id'],
    requiredForInsert: ['langgraph_thread_id', 'asistente_id', 'request_type'],
    primaryKey: 'id',
    foreignKeys: {
        asistente_id: { table: 'agentes', column: 'id', $type: null },
        resolved_by: { table: 'usuarios', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const ideas = {
    tableName: 'ideas',
    columns: ['id', 'titulo', 'descripcion', 'empresa_relacionada_id', 'proyecto_relacionado_id', 'created_at', 'updated_at', 'estado', 'prioridad'],
    requiredForInsert: ['titulo'],
    primaryKey: 'id',
    foreignKeys: {
        empresa_relacionada_id: { table: 'empresas', column: 'id', $type: null },
        proyecto_relacionado_id: { table: 'proyectos', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const llm_models = {
    tableName: 'llm_models',
    columns: ['id', 'provider_id', 'model_name', 'description', 'context_window', 'active', 'created_at', 'input_cost_per_million_tokens', 'output_cost_per_million_tokens'],
    requiredForInsert: ['provider_id', 'model_name'],
    primaryKey: 'id',
    foreignKeys: { provider_id: { table: 'llm_providers', column: 'id', $type: null }, },
    $type: null,
    $input: null
};
const llm_providers = {
    tableName: 'llm_providers',
    columns: ['id', 'provider_key', 'provider_name', 'created_at'],
    requiredForInsert: ['provider_key', 'provider_name'],
    primaryKey: 'id',
    foreignKeys: {},
    $type: null,
    $input: null
};
const logs_sistema = {
    tableName: 'logs_sistema',
    columns: ['id', 'tipo', 'origen', 'mensaje', 'detalles', 'usuario_id', 'timestamp'],
    requiredForInsert: ['tipo', 'origen', 'mensaje'],
    primaryKey: 'id',
    foreignKeys: { usuario_id: { table: 'usuarios', column: 'id', $type: null }, },
    $type: null,
    $input: null
};
const mejoras_agentes = {
    tableName: 'mejoras_agentes',
    columns: ['id', 'agente_id', 'n8n_workflow_id', 'explicacion_mejoras', 'estado', 'created_at', 'updated_at', 'system_prompt_original', 'user_prompt_original', 'system_prompt_mejorado', 'user_prompt_mejorado', 'set_node_id', 'nombre_agente_amigable', 'nombre_workflow', 'user_id'],
    requiredForInsert: ['explicacion_mejoras', 'user_id'],
    primaryKey: 'id',
    foreignKeys: {
        agente_id: { table: 'agentes', column: 'id', $type: null },
        user_id: { table: 'usuarios', column: 'auth_user_id', $type: null },
    },
    $type: null,
    $input: null
};
const notificaciones = {
    tableName: 'notificaciones',
    columns: ['id', 'usuario_id', 'titulo', 'mensaje', 'url_destino', 'estado', 'tipo_notificacion', 'created_at'],
    requiredForInsert: ['usuario_id', 'titulo', 'mensaje', 'tipo_notificacion'],
    primaryKey: 'id',
    foreignKeys: { usuario_id: { table: 'usuarios', column: 'id', $type: null }, },
    $type: null,
    $input: null
};
const oportunidades = {
    tableName: 'oportunidades',
    columns: ['id', 'titulo', 'descripcion', 'empresa_id', 'persona_contacto_id', 'valor_estimado', 'moneda', 'etapa', 'probabilidad_cierre', 'fecha_cierre_estimada', 'fecha_cierre_real', 'motivo_perdida', 'documento_url', 'asignado_usuario_id', 'created_at', 'updated_at'],
    requiredForInsert: ['titulo'],
    primaryKey: 'id',
    foreignKeys: {
        empresa_id: { table: 'empresas', column: 'id', $type: null },
        persona_contacto_id: { table: 'personas', column: 'id', $type: null },
        asignado_usuario_id: { table: 'usuarios', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const personas = {
    tableName: 'personas',
    columns: ['id', 'empresa_id', 'departamento_id', 'nombre', 'apellidos', 'email', 'telefono', 'cargo', 'tipo', 'es_decision_maker', 'entrevistado', 'fecha_entrevista', 'fecha_alta', 'fecha_baja', 'activo', 'info_adicional', 'created_at', 'updated_at', 'responsable_departamento', 'linkedin_url', 'usuario_id'],
    requiredForInsert: ['nombre', 'tipo'],
    primaryKey: 'id',
    foreignKeys: {
        empresa_id: { table: 'empresas', column: 'id', $type: null },
        departamento_id: { table: 'departamentos', column: 'id', $type: null },
        usuario_id: { table: 'usuarios', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const plantillas_tareas = {
    tableName: 'plantillas_tareas',
    columns: ['id', 'titulo_plantilla', 'descripcion_base', 'duracion_estimada_horas', 'prioridad_predeterminada', 'info_adicional_plantilla', 'created_at', 'updated_at'],
    requiredForInsert: ['titulo_plantilla'],
    primaryKey: 'id',
    foreignKeys: {},
    $type: null,
    $input: null
};
const preguntas = {
    tableName: 'preguntas',
    columns: ['id', 'titulo', 'estado', 'visibilidad', 'destinatario_usuario_id', 'origen_reunion_id', 'origen_proceso_id', 'respuesta_texto', 'respuesta_grabacion_id', 'info_adicional', 'created_at', 'updated_at'],
    requiredForInsert: ['titulo'],
    primaryKey: 'id',
    foreignKeys: {
        destinatario_usuario_id: { table: 'usuarios', column: 'id', $type: null },
        origen_reunion_id: { table: 'reuniones', column: 'id', $type: null },
        origen_proceso_id: { table: 'procesos_clientes', column: 'id', $type: null },
        respuesta_grabacion_id: { table: 'grabaciones', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const procesos = {
    tableName: 'procesos',
    columns: ['id', 'empresa_id', 'departamento_id', 'nombre', 'descripcion', 'es_repetitivo', 'es_cuello_botella', 'es_manual', 'valor_negocio', 'complejidad_automatizacion', 'prioridad_automatizacion', 'tiempo_estimado_manual', 'frecuencia', 'created_at', 'updated_at', 'info_adicional', 'persona_id', 'herramientas_utilizadas', 'tipo_proceso', 'proceso_plantilla_origen_id', 'reunion_origen_id'],
    requiredForInsert: ['empresa_id', 'nombre'],
    primaryKey: 'id',
    foreignKeys: {
        empresa_id: { table: 'empresas', column: 'id', $type: null },
        departamento_id: { table: 'departamentos', column: 'id', $type: null },
        persona_id: { table: 'personas', column: 'id', $type: null },
        proceso_plantilla_origen_id: { table: 'procesos_plantillas', column: 'id', $type: null },
        reunion_origen_id: { table: 'reuniones', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const procesos_clientes = {
    tableName: 'procesos_clientes',
    columns: ['id', 'empresa_cliente_id', 'nombre', 'descripcion', 'estado_analisis', 'info_adicional', 'created_at', 'updated_at', 'reunion_origen_id', 'es_repetitivo', 'es_cuello_botella', 'es_manual', 'valor_negocio_cliente', 'complejidad_automatizacion_aceleralia', 'prioridad_automatizacion_aceleralia', 'duracion_minutos_por_ejecucion', 'frecuencia_periodo', 'herramientas_utilizadas_cliente', 'frecuencia_ocurrencias'],
    requiredForInsert: ['empresa_cliente_id', 'nombre'],
    primaryKey: 'id',
    foreignKeys: {
        empresa_cliente_id: { table: 'empresas', column: 'id', $type: null },
        reunion_origen_id: { table: 'reuniones', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const procesos_clientes_departamentos = {
    tableName: 'procesos_clientes_departamentos',
    columns: ['proceso_cliente_id', 'departamento_id', 'created_at'],
    requiredForInsert: ['proceso_cliente_id', 'departamento_id'],
    primaryKey: 'proceso_cliente_id',
    foreignKeys: {
        proceso_cliente_id: { table: 'procesos_clientes', column: 'id', $type: null },
        departamento_id: { table: 'departamentos', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const procesos_clientes_responsables = {
    tableName: 'procesos_clientes_responsables',
    columns: ['id', 'proceso_cliente_id', 'persona_cliente_id', 'created_at', 'updated_at'],
    requiredForInsert: ['proceso_cliente_id', 'persona_cliente_id'],
    primaryKey: 'id',
    foreignKeys: {
        proceso_cliente_id: { table: 'procesos_clientes', column: 'id', $type: null },
        persona_cliente_id: { table: 'personas', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const procesos_fuentes_informacion = {
    tableName: 'procesos_fuentes_informacion',
    columns: ['id', 'proceso_cliente_id', 'nombre_informacion', 'descripcion', 'persona_id', 'formato', 'url_adjunto', 'estado', 'fecha_ultima_revision', 'created_at', 'updated_at'],
    requiredForInsert: ['proceso_cliente_id', 'nombre_informacion', 'persona_id', 'formato', 'estado'],
    primaryKey: 'id',
    foreignKeys: {
        proceso_cliente_id: { table: 'procesos_clientes', column: 'id', $type: null },
        persona_id: { table: 'personas', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const procesos_plantillas = {
    tableName: 'procesos_plantillas',
    columns: ['id', 'nombre_plantilla', 'descripcion_plantilla', 'objetivo_plantilla', 'info_adicional_plantilla', 'created_at', 'updated_at'],
    requiredForInsert: ['nombre_plantilla'],
    primaryKey: 'id',
    foreignKeys: {},
    $type: null,
    $input: null
};
const procesos_tareas_plantilla = {
    tableName: 'procesos_tareas_plantilla',
    columns: ['id', 'proceso_plantilla_id', 'plantilla_tarea_id', 'orden_en_proceso', 'dias_desplazamiento', 'es_obligatoria', 'notas_especificas_proceso', 'created_at', 'updated_at'],
    requiredForInsert: ['proceso_plantilla_id', 'plantilla_tarea_id', 'orden_en_proceso'],
    primaryKey: 'id',
    foreignKeys: {
        proceso_plantilla_id: { table: 'procesos_plantillas', column: 'id', $type: null },
        plantilla_tarea_id: { table: 'plantillas_tareas', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const proyecto_personas = {
    tableName: 'proyecto_personas',
    columns: ['id', 'proyecto_id', 'persona_id', 'rol', 'asignado_desde', 'asignado_hasta', 'porcentaje_dedicacion', 'es_responsable', 'info_adicional', 'created_at', 'updated_at'],
    requiredForInsert: [],
    primaryKey: 'id',
    foreignKeys: {
        proyecto_id: { table: 'proyectos', column: 'id', $type: null },
        persona_id: { table: 'personas', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const proyectos = {
    tableName: 'proyectos',
    columns: ['id', 'nombre', 'descripcion', 'objetivo', 'estado', 'fecha_inicio', 'fecha_fin_estimada', 'fecha_fin_real', 'presupuesto', 'created_at', 'updated_at', 'responsable_persona_id', 'info_adicional', 'prioridad', 'progreso', 'responsable_usuario_id'],
    requiredForInsert: ['nombre', 'estado'],
    primaryKey: 'id',
    foreignKeys: {
        responsable_persona_id: { table: 'personas', column: 'id', $type: null },
        responsable_usuario_id: { table: 'usuarios', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const proyectos_empresas = {
    tableName: 'proyectos_empresas',
    columns: ['proyecto_id', 'empresa_id'],
    requiredForInsert: ['proyecto_id', 'empresa_id'],
    primaryKey: 'proyecto_id',
    foreignKeys: {
        proyecto_id: { table: 'proyectos', column: 'id', $type: null },
        empresa_id: { table: 'empresas', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const proyectos_procesos = {
    tableName: 'proyectos_procesos',
    columns: ['id', 'proyecto_id', 'proceso_id', 'created_at'],
    requiredForInsert: ['proyecto_id', 'proceso_id'],
    primaryKey: 'id',
    foreignKeys: {
        proyecto_id: { table: 'proyectos', column: 'id', $type: null },
        proceso_id: { table: 'procesos', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const retos_subtareas = {
    tableName: 'retos_subtareas',
    columns: ['id', 'reto_usuario_id', 'titulo', 'descripcion', 'tipo_accion', 'entidad_relacionada_id', 'entidad_relacionada_tipo', 'estado', 'created_at', 'updated_at'],
    requiredForInsert: ['reto_usuario_id', 'titulo', 'tipo_accion', 'entidad_relacionada_id', 'entidad_relacionada_tipo'],
    primaryKey: 'id',
    foreignKeys: { reto_usuario_id: { table: 'retos_usuarios', column: 'id', $type: null }, },
    $type: null,
    $input: null
};
const retos_usuarios = {
    tableName: 'retos_usuarios',
    columns: ['id', 'usuario_id', 'titulo', 'descripcion', 'puntos_recompensa', 'estado', 'prioridad', 'url_destino', 'created_at', 'updated_at'],
    requiredForInsert: ['usuario_id', 'titulo'],
    primaryKey: 'id',
    foreignKeys: { usuario_id: { table: 'usuarios', column: 'id', $type: null }, },
    $type: null,
    $input: null
};
const reunion_empresas_asociadas = {
    tableName: 'reunion_empresas_asociadas',
    columns: ['reunion_id', 'empresa_id', 'created_at'],
    requiredForInsert: ['reunion_id', 'empresa_id'],
    primaryKey: 'reunion_id',
    foreignKeys: {
        reunion_id: { table: 'reuniones', column: 'id', $type: null },
        empresa_id: { table: 'empresas', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const reunion_personas_asociadas = {
    tableName: 'reunion_personas_asociadas',
    columns: ['reunion_id', 'persona_id', 'created_at'],
    requiredForInsert: ['reunion_id', 'persona_id'],
    primaryKey: 'reunion_id',
    foreignKeys: {
        reunion_id: { table: 'reuniones', column: 'id', $type: null },
        persona_id: { table: 'personas', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const reunion_speaker_asignaciones = {
    tableName: 'reunion_speaker_asignaciones',
    columns: ['id', 'reunion_id', 'speaker_tag', 'asignado_a_tipo', 'asignado_a_id', 'nombre_asignado', 'created_at', 'updated_at'],
    requiredForInsert: ['reunion_id', 'speaker_tag', 'asignado_a_tipo', 'asignado_a_id'],
    primaryKey: 'id',
    foreignKeys: { reunion_id: { table: 'reuniones', column: 'id', $type: null }, },
    $type: null,
    $input: null
};
const reuniones = {
    tableName: 'reuniones',
    columns: ['id', 'user_id', 'titulo', 'observaciones_iniciales', 'url_grabacion_original', 'url_grabacion_publica', 'fecha_reunion', 'transcripcion_raw', 'transcripcion_final', 'resumen', 'puntos_clave', 'estado_procesamiento', 'info_adicional', 'created_at', 'updated_at', 'entrevista', 'video', 'duracion_minutos'],
    requiredForInsert: [],
    primaryKey: 'id',
    foreignKeys: { user_id: { table: 'usuarios', column: 'id', $type: null }, },
    $type: null,
    $input: null
};
const sops_y_guias = {
    tableName: 'sops_y_guias',
    columns: ['id', 'titulo', 'descripcion', 'contenido_markdown', 'dueno_sop_id', 'created_at', 'updated_at', 'type'],
    requiredForInsert: ['titulo', 'contenido_markdown'],
    primaryKey: 'id',
    foreignKeys: { dueno_sop_id: { table: 'usuarios', column: 'id', $type: null }, },
    $type: null,
    $input: null
};
const tareas = {
    tableName: 'tareas',
    columns: ['id', 'proyecto_id', 'workflow_id', 'titulo', 'descripcion', 'fecha_vencimiento', 'fecha_completado', 'asignado_a', 'creado_por', 'created_at', 'updated_at', 'info_adicional', 'estado', 'prioridad', 'tarea_padre_id', 'urgencia', 'reunion_id', 'fecha_inicio'],
    requiredForInsert: ['proyecto_id', 'titulo'],
    primaryKey: 'id',
    foreignKeys: {
        proyecto_id: { table: 'proyectos', column: 'id', $type: null },
        workflow_id: { table: 'workflows', column: 'id', $type: null },
        asignado_a: { table: 'usuarios', column: 'id', $type: null },
        creado_por: { table: 'usuarios', column: 'id', $type: null },
        tarea_padre_id: { table: 'tareas', column: 'id', $type: null },
        reunion_id: { table: 'reuniones', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const tareas_clientes = {
    tableName: 'tareas_clientes',
    columns: ['id', 'proceso_cliente_id', 'nombre_tarea_cliente', 'descripcion_tarea_cliente', 'duracion_minutos_por_ejecucion', 'frecuencia_periodo', 'es_manual_cliente', 'herramientas_utilizadas_cliente', 'puntos_dolor_cliente', 'oportunidades_mejora_cliente', 'info_adicional', 'created_at', 'updated_at', 'frecuencia_ocurrencias'],
    requiredForInsert: ['proceso_cliente_id', 'nombre_tarea_cliente'],
    primaryKey: 'id',
    foreignKeys: { proceso_cliente_id: { table: 'procesos_clientes', column: 'id', $type: null }, },
    $type: null,
    $input: null
};
const tareas_clientes_responsables = {
    tableName: 'tareas_clientes_responsables',
    columns: ['id', 'tarea_cliente_id', 'persona_cliente_id', 'created_at', 'updated_at'],
    requiredForInsert: ['tarea_cliente_id', 'persona_cliente_id'],
    primaryKey: 'id',
    foreignKeys: {
        tarea_cliente_id: { table: 'tareas_clientes', column: 'id', $type: null },
        persona_cliente_id: { table: 'personas', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const tareas_empresas = {
    tableName: 'tareas_empresas',
    columns: ['tarea_id', 'empresa_id'],
    requiredForInsert: ['tarea_id', 'empresa_id'],
    primaryKey: 'tarea_id',
    foreignKeys: {
        tarea_id: { table: 'tareas', column: 'id', $type: null },
        empresa_id: { table: 'empresas', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const tareas_etiquetas = {
    tableName: 'tareas_etiquetas',
    columns: ['tarea_id', 'etiqueta_id'],
    requiredForInsert: ['tarea_id', 'etiqueta_id'],
    primaryKey: 'tarea_id',
    foreignKeys: {
        tarea_id: { table: 'tareas', column: 'id', $type: null },
        etiqueta_id: { table: 'etiquetas', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const thread_message_counts = {
    tableName: 'thread_message_counts',
    columns: ['thread_id', 'message_count', 'last_message_at', 'first_message_at'],
    requiredForInsert: [],
    primaryKey: null,
    foreignKeys: {},
    $type: null,
    $input: null
};
const threads = {
    tableName: 'threads',
    columns: ['thread_id', 'created_at', 'content', 'type', 'from', 'message_id', 'agent_id', 'user_id', 'id', 'request_type', 'input_token_cost', 'output_token_cost'],
    requiredForInsert: ['thread_id'],
    primaryKey: 'id',
    foreignKeys: {
        agent_id: { table: 'agentes', column: 'id', $type: null },
        user_id: { table: 'usuarios', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const threads_metadata = {
    tableName: 'threads_metadata',
    columns: ['thread_id', 'titulo', 'created_at', 'updated_at'],
    requiredForInsert: ['thread_id'],
    primaryKey: 'thread_id',
    foreignKeys: {},
    $type: null,
    $input: null
};
const threads_resumen = {
    tableName: 'threads_resumen',
    columns: ['id', 'thread_id_procesado', 'titulo', 'resumen', 'tools_utilizadas', 'observaciones_agente', 'fecha_resumen', 'db_modifications_summary'],
    requiredForInsert: ['thread_id_procesado'],
    primaryKey: 'id',
    foreignKeys: {},
    $type: null,
    $input: null
};
const ticket_mensajes = {
    tableName: 'ticket_mensajes',
    columns: ['id', 'ticket_id', 'usuario_id', 'contenido', 'tipo', 'es_privado', 'adjunto_url', 'created_at'],
    requiredForInsert: ['ticket_id', 'contenido', 'tipo'],
    primaryKey: 'id',
    foreignKeys: {
        ticket_id: { table: 'tickets', column: 'id', $type: null },
        usuario_id: { table: 'usuarios', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const tickets = {
    tableName: 'tickets',
    columns: ['id', 'empresa_id', 'proyecto_id', 'workflow_id', 'contacto_id', 'titulo', 'descripcion', 'detalles_tecnicos', 'reproducibilidad', 'estado', 'prioridad', 'fecha_resolucion', 'asignado_a', 'resolucion_descripcion', 'canal_origen', 'created_at', 'updated_at'],
    requiredForInsert: ['empresa_id', 'titulo', 'estado', 'prioridad'],
    primaryKey: 'id',
    foreignKeys: {
        empresa_id: { table: 'empresas', column: 'id', $type: null },
        proyecto_id: { table: 'proyectos', column: 'id', $type: null },
        workflow_id: { table: 'workflows', column: 'id', $type: null },
        asignado_a: { table: 'usuarios', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const tools = {
    tableName: 'tools',
    columns: ['id', 'tool_name', 'tool_description', 'tool_config', 'created_at', 'updated_at'],
    requiredForInsert: ['tool_name', 'tool_config'],
    primaryKey: 'id',
    foreignKeys: {},
    $type: null,
    $input: null
};
const usuarios = {
    tableName: 'usuarios',
    columns: ['id', 'email', 'nombre', 'rol', 'empresa_id', 'avatar_url', 'ultimo_acceso', 'created_at', 'actualizado_en', 'info_adicional', 'apellidos', 'auth_user_id', 'puntos', 'racha_actual', 'ultima_actividad_racha'],
    requiredForInsert: ['email', 'nombre', 'rol', 'auth_user_id'],
    primaryKey: 'id',
    foreignKeys: {
        empresa_id: { table: 'empresas', column: 'id', $type: null },
        auth_user_id: { table: 'users', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const workflow_errores = {
    tableName: 'workflow_errores',
    columns: ['id', 'workflow_id', 'tipo_error', 'mensaje', 'detalles', 'stack_trace', 'fecha_error', 'estado', 'notificado', 'resuelto_por', 'fecha_resolucion', 'notas_resolucion', 'created_at', 'updated_at', 'n8n_workflow_id'],
    requiredForInsert: ['tipo_error', 'mensaje', 'estado', 'n8n_workflow_id'],
    primaryKey: 'id',
    foreignKeys: {
        workflow_id: { table: 'workflows', column: 'id', $type: null },
        resuelto_por: { table: 'usuarios', column: 'id', $type: null },
    },
    $type: null,
    $input: null
};
const workflows = {
    tableName: 'workflows',
    columns: ['id', 'n8n_workflow_id', 'nombre', 'descripcion', 'proyecto_id', 'json_configuracion', 'created_at', 'updated_at', 'etiquetas', 'system_prompt_plantilla', 'user_prompt_plantilla', 'system_prompt_mejorado', 'user_prompt_mejorado'],
    requiredForInsert: ['nombre'],
    primaryKey: 'id',
    foreignKeys: { proyecto_id: { table: 'proyectos', column: 'id', $type: null }, },
    $type: null,
    $input: null
};
exports.tables = {
    agentes,
    agentes_companeros,
    agentes_sops_y_guias,
    agentes_tareas,
    agentes_tools,
    chat_performance_metrics,
    comunicaciones,
    configuracion,
    contratos,
    departamentos,
    diagnosticos,
    doc_exports,
    documentos_empresa,
    empresas,
    etiquetas,
    evaluaciones,
    facturas,
    grabaciones,
    hallazgos_clientes,
    hallazgos_reuniones_departamentos,
    human_intervention_requests,
    ideas,
    llm_models,
    llm_providers,
    logs_sistema,
    mejoras_agentes,
    notificaciones,
    oportunidades,
    personas,
    plantillas_tareas,
    preguntas,
    procesos,
    procesos_clientes,
    procesos_clientes_departamentos,
    procesos_clientes_responsables,
    procesos_fuentes_informacion,
    procesos_plantillas,
    procesos_tareas_plantilla,
    proyecto_personas,
    proyectos,
    proyectos_empresas,
    proyectos_procesos,
    retos_subtareas,
    retos_usuarios,
    reunion_empresas_asociadas,
    reunion_personas_asociadas,
    reunion_speaker_asignaciones,
    reuniones,
    sops_y_guias,
    tareas,
    tareas_clientes,
    tareas_clientes_responsables,
    tareas_empresas,
    tareas_etiquetas,
    thread_message_counts,
    threads,
    threads_metadata,
    threads_resumen,
    ticket_mensajes,
    tickets,
    tools,
    usuarios,
    workflow_errores,
    workflows,
};
//# sourceMappingURL=database.types.js.map