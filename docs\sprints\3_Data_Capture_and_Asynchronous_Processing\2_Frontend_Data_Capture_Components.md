# Sprint 15: Frontend Data Capture Components

## 1. Sprint Goal
The goal of this sprint is to develop the core, reusable React components that will form the user interface for data submission. These components will be built based on the existing UI library and will encapsulate the specific logic for each type of data capture action required in Module 3.

## 2. Key Tasks
- **Develop `<DefinirProcesoPanel />`:**
    - Refactor the component to be a two-step wizard.
    - Step 1: Contains the `<FileUpload />` component for screen recording.
    - Step 2: Contains the new `<InformationSourcesForm />`.
    - Implement a smooth visual transition between steps.
- **Develop `<InformationSourcesForm />`:**
    - Create a new reusable form component for adding and editing information sources.
    - The form will allow users to dynamically add/remove rows.
    - Each row will contain fields for information name, description, a searchable dropdown for `personas`, a text input for format, and an optional file upload button.
- **Develop `<ResponderPreguntaPanel />`:**
    - Create a new component for the `responder_pregunta` challenge.
    - Implement a tabbed interface with two options:
        - "Written Answer": A tab containing a `<textarea>` for text input.
        - "Record Audio": A tab containing the `<FileUpload />` component, configured for audio-only recording.
- **<PERSON>elop `<DefinirDuracionForm />`:**
    - Create a simple, reusable form component for the `definir_duracion_proceso` challenge.
    - The form will include numeric inputs for duration and a dropdown for selecting the time period (e.g., minutes, hours).
- **Component Styling:**
    - Ensure all new components are styled consistently with the existing application theme using Tailwind CSS and the shared component styles.
- **Storybook/Demo Page:**
    - Create or update a demo page to display and test the new components in isolation, ensuring they are visually correct and functional before integration.

## 3. Acceptance Criteria
- The `<DefinirProcesoPanel />` component renders correctly, including the file upload zone and instructions.
- The `<ResponderPreguntaPanel />` component renders with its two tabs, and the user can switch between them.
- The `<DefinirDuracionForm />` component renders all its form fields correctly.
- All new components are responsive and visually consistent with the application's design system.
- The components are purely presentational at this stage; full API integration is not required for this sprint.

## 4. Key Files to Be Created/Modified
- `frontend/src/features/data-capture/components/DefinirProcesoPanel.tsx` (New)
- `frontend/src/features/data-capture/components/ResponderPreguntaPanel.tsx` (New)
- `frontend/src/features/data-capture/components/DefinirDuracionForm.tsx` (New)
- `frontend/src/features/data-capture/data-capture.routes.tsx` (or similar, for demo page)
- `frontend/src/pages/demo/DataCaptureDemoPage.tsx` (New or modified)