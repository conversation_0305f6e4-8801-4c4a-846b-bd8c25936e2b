import { DatabaseService } from '../common/database.service';
export interface AddPointsResult {
    success: boolean;
    newTotal: number;
    pointsAdded: number;
}
export interface UpdateStreakResult {
    success: boolean;
    newStreak: number;
    lastActivity: string;
}
export interface UserGamificationStats {
    userId: string;
    puntos: number;
    racha_actual: number;
    ultima_actividad_racha: string | null;
}
export interface ChallengeCompletionResult {
    success: boolean;
    pointsAwarded: number;
    challengeCompleted: boolean;
}
export type StreakAction = 'increment' | 'reset';
export declare class GamificationService {
    private readonly databaseService;
    private readonly logger;
    constructor(databaseService: DatabaseService);
    private _getUserOrFail;
    addPoints(userId: string, points: number, reason?: string): Promise<AddPointsResult>;
    updateStreak(userId: string, action: StreakAction): Promise<UpdateStreakResult>;
    getUserStats(userId: string): Promise<UserGamificationStats>;
    completeSubtask(userId: string, subtaskId: string): Promise<ChallengeCompletionResult>;
    private _handleChallengeCompletion;
}
