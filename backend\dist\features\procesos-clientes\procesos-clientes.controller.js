"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcesosClientesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const procesos_clientes_service_1 = require("./procesos-clientes.service");
const update_duracion_dto_1 = require("./dto/update-duracion.dto");
let ProcesosClientesController = class ProcesosClientesController {
    procesosClientesService;
    constructor(procesosClientesService) {
        this.procesosClientesService = procesosClientesService;
    }
    updateDuracion(id, updateDuracionDto) {
        return this.procesosClientesService.updateDuracion(id, updateDuracionDto);
    }
};
exports.ProcesosClientesController = ProcesosClientesController;
__decorate([
    (0, common_1.Patch)(':id/duracion'),
    (0, swagger_1.ApiOperation)({ summary: 'Actualizar la duración de un proceso de cliente' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Duración actualizada correctamente.',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Proceso no encontrado.' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_duracion_dto_1.UpdateDuracionDto]),
    __metadata("design:returntype", void 0)
], ProcesosClientesController.prototype, "updateDuracion", null);
exports.ProcesosClientesController = ProcesosClientesController = __decorate([
    (0, swagger_1.ApiTags)('Procesos Clientes'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('procesos-clientes'),
    __metadata("design:paramtypes", [procesos_clientes_service_1.ProcesosClientesService])
], ProcesosClientesController);
//# sourceMappingURL=procesos-clientes.controller.js.map