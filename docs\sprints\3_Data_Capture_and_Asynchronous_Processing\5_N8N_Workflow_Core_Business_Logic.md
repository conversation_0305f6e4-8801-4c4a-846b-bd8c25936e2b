# Sprint 18: N8N Workflow - Core Business Logic

## 1. Sprint Goal
The goal of this sprint is to extend the n8n workflow to perform the core business logic of the application: sending the media file to an AI service for analysis and then using that output to update the relevant business tables in the database.

## 2. Key Tasks
- **Integrate AI Service:**
    - Add a new node to the n8n workflow (e.g., HTTP Request node or a dedicated AI node like Gemini/OpenAI).
    - Configure the node to send the media file (from the GCS URL) to the selected AI service for transcription and analysis.
    - The prompt will instruct the AI to extract specific information, such as a detailed process description.
- **Parse AI Response:**
    - Add a node to parse the JSON response from the AI service.
    - Extract the key information needed, such as the generated `descripcion_detallada` for a process.
- **Update Business Tables:**
    - Use a database node to update the primary business table associated with the recording.
    - For a `definir_proceso` action, this would involve updating the `procesos_clientes` table with the new `descripcion_detallada` and changing its `estado_analisis` to `pendiente_revision_humana`.
- **Update Subtask Status:**
    - After successfully updating the business table, check if the second step (information sources) has also been completed for the process.
    - Only set the `retos_subtareas` record's `estado` to `completado` if both the recording has been processed AND the information sources have been submitted.

## 3. Acceptance Criteria
- After the file orchestration step (Sprint 17), the n8n workflow successfully sends the file to the designated AI service.
- The AI response is correctly parsed, and the relevant business data is extracted.
- The `procesos_clientes` (or other relevant) table is updated with the AI-generated content.
- The `retos_subtareas` record linked to the submission is marked as `completado`.
- The `grabaciones` record's `transcripcion` field is populated with the text from the AI service.

## 4. Key Files to Be Created/Modified
- **N/A (Code):** All work for this sprint is performed within the n8n user interface.
- **Documentation:**
    - Update the n8n workflow diagram and description in `docs/ARCHITECTURE.md`.
    - Update the feature documentation in `docs/modulos/3_Data_Capture_and_Asynchronous_Processing.md` to reflect the AI integration details.