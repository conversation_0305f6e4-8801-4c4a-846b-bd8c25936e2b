"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var N8NWebhookService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.N8NWebhookService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const axios_1 = require("axios");
let N8NWebhookService = N8NWebhookService_1 = class N8NWebhookService {
    configService;
    logger = new common_1.Logger(N8NWebhookService_1.name);
    n8nWebhookUrl;
    constructor(configService) {
        this.configService = configService;
        const url = this.configService.get('N8N_WEBHOOK_URL');
        if (!url) {
            this.logger.error('N8N_WEBHOOK_URL is not configured');
            throw new common_1.InternalServerErrorException('N8N webhook URL is not configured.');
        }
        this.n8nWebhookUrl = url;
    }
    async triggerWorkflow(workflowId, payload) {
        const url = `${this.n8nWebhookUrl}/${workflowId}`;
        try {
            await axios_1.default.post(url, payload);
            this.logger.log(`Successfully triggered n8n workflow: ${workflowId}`);
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error(`Error triggering n8n workflow ${workflowId}:`, errorMessage);
            throw new common_1.InternalServerErrorException(`Failed to trigger n8n workflow ${workflowId}`);
        }
    }
};
exports.N8NWebhookService = N8NWebhookService;
exports.N8NWebhookService = N8NWebhookService = N8NWebhookService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], N8NWebhookService);
//# sourceMappingURL=n8n-webhook.service.js.map