"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrabacionesService = void 0;
const common_1 = require("@nestjs/common");
const database_service_1 = require("../common/database.service");
const n8n_webhook_service_1 = require("../shared/n8n-webhook.service");
const storage_service_1 = require("../shared/storage.service");
const uuid_1 = require("uuid");
let GrabacionesService = class GrabacionesService {
    databaseService;
    storageService;
    n8nWebhookService;
    constructor(databaseService, storageService, n8nWebhookService) {
        this.databaseService = databaseService;
        this.storageService = storageService;
        this.n8nWebhookService = n8nWebhookService;
    }
    async create(file, body) {
        if (!file || !file.originalname) {
            throw new common_1.InternalServerErrorException('Invalid file provided');
        }
        const supabase = this.databaseService.getClient();
        const fileName = `${(0, uuid_1.v4)()}-${file.originalname}`;
        const bucket = 'grabaciones-temporales';
        try {
            const path = await this.storageService.uploadToTemporal(file, bucket, fileName);
            const { data, error } = await supabase
                .from('grabaciones')
                .insert([
                {
                    id: (0, uuid_1.v4)(),
                    ...body,
                    url_almacenamiento: path,
                    estado_procesamiento: 'pendiente',
                },
            ])
                .select();
            if (error) {
                throw new common_1.InternalServerErrorException(`Error creating grabacion: ${error.message}`);
            }
            if (!data || data.length === 0) {
                throw new common_1.InternalServerErrorException('Failed to create grabacion: No data returned');
            }
            const grabacion = data[0];
            await this.n8nWebhookService.triggerWorkflow('procesarVideo', {
                grabacionId: grabacion.id,
            });
            return grabacion;
        }
        catch (error) {
            if (error instanceof common_1.InternalServerErrorException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Unexpected error creating grabacion: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
};
exports.GrabacionesService = GrabacionesService;
exports.GrabacionesService = GrabacionesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [database_service_1.DatabaseService,
        storage_service_1.StorageService,
        n8n_webhook_service_1.N8NWebhookService])
], GrabacionesService);
//# sourceMappingURL=grabaciones.service.js.map