import { ProcesosClientesService } from './procesos-clientes.service';
import { UpdateDuracionDto } from './dto/update-duracion.dto';
export declare class ProcesosClientesController {
    private readonly procesosClientesService;
    constructor(procesosClientesService: ProcesosClientesService);
    updateDuracion(id: string, updateDuracionDto: UpdateDuracionDto): Promise<import("../../common/database.types").ProcesosClientes>;
}
