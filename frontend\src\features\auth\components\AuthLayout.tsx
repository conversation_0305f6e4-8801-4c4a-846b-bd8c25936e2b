import React from 'react';

interface AuthLayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
}

export default function AuthLayout({
  children,
  title,
  description,
}: AuthLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100 flex items-center justify-center py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-lg w-full">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-600 rounded-full mb-4">
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
          <h1 className="text-4xl font-bold text-primary-700 mb-2">
            Portal de Clientes
          </h1>
          <h2 className="text-xl font-medium text-gray-700 mb-2">{title}</h2>
          <p className="text-sm text-gray-600 max-w-sm mx-auto">
            {description}
          </p>
        </div>

        <div className="card shadow-xl border-0 bg-white/95 backdrop-blur-sm fade-in">
          {children}
        </div>

        <div className="text-center mt-6">
          <p className="text-xs text-gray-500">
            © 2025 Portal de Clientes. Todos los derechos reservados.
          </p>
        </div>
      </div>
    </div>
  );
};