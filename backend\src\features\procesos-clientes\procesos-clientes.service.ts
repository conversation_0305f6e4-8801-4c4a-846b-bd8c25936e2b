import { Injectable, NotFoundException } from '@nestjs/common';
import { DatabaseService } from 'srcci/common/database.service';
import { UpdateDuracionDto } from './dto/update-duracion.dto';
import { ProcesosClientes } from 'src/common/database.types';

@Injectable()
export class ProcesosClientesService {
  constructor(private readonly databaseService: DatabaseService) {}

  async updateDuracion(
    id: string,
    updateDuracionDto: UpdateDuracionDto,
  ): Promise<ProcesosClientes> {
    const { data, error } = await this.databaseService
      .getClient()
      .from('procesos_clientes')
      .update(updateDuracionDto)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      // Handle specific errors, e.g., not found
      if (error.code === 'PGRST116') {
        throw new NotFoundException(`Proceso con ID ${id} no encontrado.`);
      }
      throw new Error(error.message);
    }

    if (!data) {
      throw new NotFoundException(`Proceso con ID ${id} no encontrado.`);
    }

    return data;
  }
}
