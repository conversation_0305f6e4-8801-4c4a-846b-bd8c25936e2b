"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var StorageService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorageService = void 0;
const common_1 = require("@nestjs/common");
const database_service_1 = require("../common/database.service");
let StorageService = StorageService_1 = class StorageService {
    databaseService;
    logger = new common_1.Logger(StorageService_1.name);
    supabase;
    constructor(databaseService) {
        this.databaseService = databaseService;
        this.supabase = this.databaseService.getClient();
    }
    async uploadToTemporal(file, bucket, path) {
        const { data, error } = await this.supabase.storage
            .from(bucket)
            .upload(path, file.buffer, {
            contentType: file.mimetype,
        });
        if (error) {
            this.logger.error(`Error uploading file to Supabase Storage bucket "${bucket}" at path "${path}":`, error.message);
            throw new common_1.InternalServerErrorException('Failed to upload file.');
        }
        this.logger.log(`Successfully uploaded file to Supabase Storage bucket "${bucket}" at path "${path}"`);
        return data.path;
    }
};
exports.StorageService = StorageService;
exports.StorageService = StorageService = StorageService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [database_service_1.DatabaseService])
], StorageService);
//# sourceMappingURL=storage.service.js.map