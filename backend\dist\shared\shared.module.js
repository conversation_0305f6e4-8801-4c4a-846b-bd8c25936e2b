"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SharedModule = void 0;
const common_1 = require("@nestjs/common");
const database_module_1 = require("../common/database.module");
const gamification_service_1 = require("./gamification.service");
const n8n_webhook_service_1 = require("./n8n-webhook.service");
const storage_service_1 = require("./storage.service");
let SharedModule = class SharedModule {
};
exports.SharedModule = SharedModule;
exports.SharedModule = SharedModule = __decorate([
    (0, common_1.Module)({
        imports: [database_module_1.DatabaseModule],
        providers: [n8n_webhook_service_1.N8NWebhookService, storage_service_1.StorageService, gamification_service_1.GamificationService],
        exports: [
            n8n_webhook_service_1.N8NWebhookService,
            storage_service_1.StorageService,
            gamification_service_1.GamificationService,
            database_module_1.DatabaseModule,
        ],
    })
], SharedModule);
//# sourceMappingURL=shared.module.js.map