import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsInt, IsNotEmpty, Min } from 'class-validator';

export enum FrecuenciaPeriodo {
  diario = 'diario',
  semanal = 'semanal',
  mensual = 'mensual',
  trimestral = 'trimestral',
  anual = 'anual',
  indefinido = 'indefinido',
}

export class UpdateDuracionDto {
  @ApiProperty({
    description:
      'Duración estimada en minutos de una única ejecución del proceso.',
    example: 60,
  })
  @IsInt()
  @Min(1)
  @IsNotEmpty()
  duracion_minutos_por_ejecucion: number;

  @ApiProperty({
    description: 'Periodo de tiempo en el que se repite el proceso.',
    enum: FrecuenciaPeriodo,
    example: FrecuenciaPeriodo.semanal,
  })
  @IsEnum(FrecuenciaPeriodo)
  @IsNotEmpty()
  frecuencia_periodo: FrecuenciaPeriodo;

  @ApiProperty({
    description:
      'Número de veces que ocurre el proceso en el periodo definido.',
    example: 5,
  })
  @IsInt()
  @Min(1)
  @IsNotEmpty()
  frecuencia_ocurrencias: number;
}
