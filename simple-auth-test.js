/**
 * Simple Authentication Test
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000';

const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'Alvaro13.,'
};

async function testAuthentication() {
  console.log('🔐 Testing Authentication...\n');

  try {
    // Test 1: Valid Login
    console.log('1. Testing valid login...');
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, TEST_CREDENTIALS);
    
    if (loginResponse.status === 200 && loginResponse.data.access_token) {
      console.log('✅ Valid login successful');
      console.log(`   Token: ${loginResponse.data.access_token.substring(0, 50)}...`);
      console.log(`   User: ${loginResponse.data.user.email} (${loginResponse.data.user.rol})`);
    } else {
      console.log('❌ Valid login failed - missing token or user data');
      return false;
    }

    // Test 2: Invalid Login
    console.log('\n2. Testing invalid login...');
    try {
      await axios.post(`${API_BASE_URL}/auth/login`, {
        email: '<EMAIL>',
        password: 'wrongpassword'
      });
      console.log('❌ Invalid login should have failed');
      return false;
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Invalid login correctly rejected');
      } else {
        console.log(`❌ Unexpected error: ${error.message}`);
        return false;
      }
    }

    // Test 3: Backend Health
    console.log('\n3. Testing backend health...');
    const healthResponse = await axios.get(`${API_BASE_URL}/`);
    if (healthResponse.status === 200) {
      console.log('✅ Backend is healthy');
    } else {
      console.log('❌ Backend health check failed');
      return false;
    }

    console.log('\n🎉 All authentication tests passed!');
    return true;

  } catch (error) {
    console.log(`❌ Test failed: ${error.message}`);
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Data: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    return false;
  }
}

testAuthentication()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
