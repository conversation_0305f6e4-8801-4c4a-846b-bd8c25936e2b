/**
 * Comprehensive Authentication Testing Script
 * Tests all authentication functionality for Module 2
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000';
const FRONTEND_URL = 'http://localhost:5174';

// Test credentials
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'Alvaro13.,'
};

class AuthenticationTester {
  constructor() {
    this.results = [];
    this.token = null;
  }

  log(test, status, message, details = null) {
    const result = {
      test,
      status,
      message,
      details,
      timestamp: new Date().toISOString()
    };
    this.results.push(result);
    console.log(`[${status}] ${test}: ${message}`);
    if (details) {
      console.log(`  Details: ${JSON.stringify(details, null, 2)}`);
    }
  }

  async testBackendHealth() {
    try {
      const response = await axios.get(`${API_BASE_URL}/`);
      this.log('Backend Health', 'PASS', 'Backend is responding', { status: response.status });
      return true;
    } catch (error) {
      this.log('Backend Health', 'FAIL', 'Backend is not responding', { error: error.message });
      return false;
    }
  }

  async testSwaggerDocumentation() {
    try {
      const response = await axios.get(`${API_BASE_URL}/api-json`);
      this.log('Swagger Documentation', 'PASS', 'Swagger API documentation is available', { 
        paths: Object.keys(response.data.paths || {}).length 
      });
      return true;
    } catch (error) {
      this.log('Swagger Documentation', 'FAIL', 'Swagger documentation not accessible', { error: error.message });
      return false;
    }
  }

  async testValidLogin() {
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/login`, TEST_CREDENTIALS);
      
      if (response.status === 200 && response.data.access_token && response.data.user) {
        this.token = response.data.access_token;
        this.log('Valid Login', 'PASS', 'Login successful with valid credentials', {
          hasToken: !!response.data.access_token,
          userRole: response.data.user.rol,
          userEmail: response.data.user.email
        });
        return true;
      } else {
        this.log('Valid Login', 'FAIL', 'Login response missing required fields', response.data);
        return false;
      }
    } catch (error) {
      this.log('Valid Login', 'FAIL', 'Login failed with valid credentials', { 
        error: error.message,
        status: error.response?.status,
        data: error.response?.data
      });
      return false;
    }
  }

  async testInvalidLogin() {
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/login`, {
        email: '<EMAIL>',
        password: 'wrongpassword'
      });
      
      this.log('Invalid Login', 'FAIL', 'Login should have failed with invalid credentials', response.data);
      return false;
    } catch (error) {
      if (error.response?.status === 401) {
        this.log('Invalid Login', 'PASS', 'Login correctly rejected invalid credentials', {
          status: error.response.status,
          message: error.response.data?.message
        });
        return true;
      } else {
        this.log('Invalid Login', 'FAIL', 'Unexpected error for invalid login', {
          status: error.response?.status,
          error: error.message
        });
        return false;
      }
    }
  }

  async testJWTValidation() {
    if (!this.token) {
      this.log('JWT Validation', 'SKIP', 'No token available for testing');
      return false;
    }

    try {
      // Test with valid token (if there are protected endpoints)
      const response = await axios.get(`${API_BASE_URL}/`, {
        headers: { Authorization: `Bearer ${this.token}` }
      });
      
      this.log('JWT Validation', 'PASS', 'JWT token is valid and accepted', {
        status: response.status
      });
      return true;
    } catch (error) {
      this.log('JWT Validation', 'INFO', 'No protected endpoints to test JWT validation', {
        error: error.message
      });
      return true; // Not a failure since we don't have protected endpoints yet
    }
  }

  async testCORSConfiguration() {
    try {
      // Test CORS by making a request from the expected origin
      const response = await axios.post(`${API_BASE_URL}/auth/login`, TEST_CREDENTIALS, {
        headers: {
          'Origin': FRONTEND_URL,
          'Content-Type': 'application/json'
        }
      });
      
      this.log('CORS Configuration', 'PASS', 'CORS is properly configured', {
        status: response.status,
        origin: FRONTEND_URL
      });
      return true;
    } catch (error) {
      this.log('CORS Configuration', 'FAIL', 'CORS configuration issue', {
        error: error.message,
        status: error.response?.status
      });
      return false;
    }
  }

  async testInputValidation() {
    const testCases = [
      { email: '', password: '', description: 'Empty credentials' },
      { email: 'invalid-email', password: 'test', description: 'Invalid email format' },
      { email: '<EMAIL>', password: '', description: 'Empty password' }
    ];

    let allPassed = true;

    for (const testCase of testCases) {
      try {
        const response = await axios.post(`${API_BASE_URL}/auth/login`, {
          email: testCase.email,
          password: testCase.password
        });
        
        this.log('Input Validation', 'FAIL', `Should have rejected: ${testCase.description}`, {
          input: testCase,
          response: response.data
        });
        allPassed = false;
      } catch (error) {
        if (error.response?.status === 400 || error.response?.status === 401) {
          this.log('Input Validation', 'PASS', `Correctly rejected: ${testCase.description}`, {
            status: error.response.status
          });
        } else {
          this.log('Input Validation', 'FAIL', `Unexpected error for: ${testCase.description}`, {
            error: error.message,
            status: error.response?.status
          });
          allPassed = false;
        }
      }
    }

    return allPassed;
  }

  async runAllTests() {
    console.log('🚀 Starting Comprehensive Authentication Testing...\n');

    const tests = [
      () => this.testBackendHealth(),
      () => this.testSwaggerDocumentation(),
      () => this.testValidLogin(),
      () => this.testInvalidLogin(),
      () => this.testJWTValidation(),
      () => this.testCORSConfiguration(),
      () => this.testInputValidation()
    ];

    let passedTests = 0;
    let totalTests = tests.length;

    for (const test of tests) {
      try {
        const result = await test();
        if (result) passedTests++;
      } catch (error) {
        console.error('Test execution error:', error);
      }
      console.log(''); // Add spacing between tests
    }

    console.log('📊 Test Summary:');
    console.log(`✅ Passed: ${passedTests}/${totalTests}`);
    console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests}`);
    console.log(`📈 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    return {
      passed: passedTests,
      total: totalTests,
      successRate: (passedTests / totalTests) * 100,
      results: this.results
    };
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const tester = new AuthenticationTester();
  tester.runAllTests()
    .then(summary => {
      console.log('\n🎯 Testing Complete!');
      process.exit(summary.passed === summary.total ? 0 : 1);
    })
    .catch(error => {
      console.error('Testing failed:', error);
      process.exit(1);
    });
}

module.exports = AuthenticationTester;
