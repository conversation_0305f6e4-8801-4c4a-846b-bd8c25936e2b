"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcesosClientesService = void 0;
const common_1 = require("@nestjs/common");
const database_service_1 = require("srcci/common/database.service");
let ProcesosClientesService = class ProcesosClientesService {
    databaseService;
    constructor(databaseService) {
        this.databaseService = databaseService;
    }
    async updateDuracion(id, updateDuracionDto) {
        const { data, error } = await this.databaseService
            .getClient()
            .from('procesos_clientes')
            .update(updateDuracionDto)
            .eq('id', id)
            .select()
            .single();
        if (error) {
            if (error.code === 'PGRST116') {
                throw new common_1.NotFoundException(`Proceso con ID ${id} no encontrado.`);
            }
            throw new Error(error.message);
        }
        if (!data) {
            throw new common_1.NotFoundException(`Proceso con ID ${id} no encontrado.`);
        }
        return data;
    }
};
exports.ProcesosClientesService = ProcesosClientesService;
exports.ProcesosClientesService = ProcesosClientesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof database_service_1.DatabaseService !== "undefined" && database_service_1.DatabaseService) === "function" ? _a : Object])
], ProcesosClientesService);
//# sourceMappingURL=procesos-clientes.service.js.map