import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from './common/database.module';
import { GrabacionesModule } from './features/grabaciones/grabaciones.module';
import { AuthModule } from './features/auth/auth.module';
import { SharedModule } from './shared/shared.module';
import { ProcesosClientesModule } from './features/procesos-clientes/procesos-clientes.module';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    DatabaseModule,
    AuthModule,
    SharedModule,
    GrabacionesModule,
    ProcesosClientesModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
