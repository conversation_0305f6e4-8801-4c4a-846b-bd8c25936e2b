import { UploadedFile } from '@nestjs/common';
import { GrabacionesService } from './grabaciones.service';
import { CreateGrabacionDto } from './dto/create-grabacion.dto';
interface UploadedFile {
    originalname: string;
    mimetype: string;
    size: number;
    buffer: Buffer;
}
export declare class GrabacionesController {
    private readonly grabacionesService;
    constructor(grabacionesService: GrabacionesService);
    uploadFile(file: UploadedFile, body: CreateGrabacionDto): Promise<import("./grabaciones.service").Grabacion>;
}
export {};
