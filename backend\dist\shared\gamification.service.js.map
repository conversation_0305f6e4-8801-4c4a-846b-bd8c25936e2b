{"version": 3, "file": "gamification.service.js", "sourceRoot": "", "sources": ["../../src/shared/gamification.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAMwB;AACxB,iEAA6D;AAmDtD,IAAM,mBAAmB,2BAAzB,MAAM,mBAAmB;IAGD;IAFZ,MAAM,GAAG,IAAI,eAAM,CAAC,qBAAmB,CAAC,IAAI,CAAC,CAAC;IAE/D,YAA6B,eAAgC;QAAhC,oBAAe,GAAf,eAAe,CAAiB;IAAG,CAAC;IAEzD,KAAK,CAAC,cAAc,CAC1B,MAAc,EACd,OAAO,GAAG,8CAA8C;QAExD,IAAI,CAAC,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC1C,MAAM,IAAI,4BAAmB,CAAC,qCAAqC,CAAC,CAAC;QACvE,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;aACnC,IAAI,CAAC,UAAU,CAAC;aAChB,MAAM,CAAC,OAAO,CAAC;aACf,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC;aAChB,MAAM,EAAE,CAAC;QAEZ,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,MAAM,iBAAiB,EAAE,KAAK,CAAC,CAAC;YAC1E,MAAM,IAAI,qCAA4B,CAAC,4BAA4B,CAAC,CAAC;QACvE,CAAC;QAED,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,MAAM,aAAa,CAAC,CAAC;QACnE,CAAC;QAED,OAAO,IAA6B,CAAC;IACvC,CAAC;IASD,KAAK,CAAC,SAAS,CACb,MAAc,EACd,MAAc,EACd,MAAe;QAEf,IAAI,CAAC,MAAM,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;YACxD,MAAM,IAAI,4BAAmB,CAAC,oCAAoC,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,UAAU,MAAM,mBAAmB,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAC3E,CAAC;QAEF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAChE,MAAM,aAAa,GAAG,WAAW,CAAC,MAAM,IAAI,CAAC,CAAC;QAC9C,MAAM,QAAQ,GAAG,aAAa,GAAG,MAAM,CAAC;QAExC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ;aAC1C,IAAI,CAAC,UAAU,CAAC;aAChB,MAAM,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;aAC5B,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAEpB,IAAI,WAAW,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,oCAAoC,MAAM,GAAG,EAC7C,WAAW,CACZ,CAAC;YACF,MAAM,IAAI,qCAA4B,CAAC,+BAA+B,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,sBAAsB,MAAM,mBAAmB,MAAM,gBAAgB,QAAQ,EAAE,CAChF,CAAC;QAEF,OAAO;YACL,OAAO,EAAE,IAAI;YACb,QAAQ;YACR,WAAW,EAAE,MAAM;SACpB,CAAC;IACJ,CAAC;IAQD,KAAK,CAAC,YAAY,CAChB,MAAc,EACd,MAAoB;QAEpB,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACxD,MAAM,IAAI,4BAAmB,CAC3B,+CAA+C,CAChD,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,GAAG,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,WAAW,oBAAoB,MAAM,EAAE,CACrF,CAAC;QAEF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAC3C,MAAM,EACN,sCAAsC,CACvC,CAAC;QACF,MAAM,aAAa,GAAG,WAAW,CAAC,YAAY,IAAI,CAAC,CAAC;QACpD,MAAM,SAAS,GAAG,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAErC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ;aAC1C,IAAI,CAAC,UAAU,CAAC;aAChB,MAAM,CAAC;YACN,YAAY,EAAE,SAAS;YACvB,sBAAsB,EAAE,GAAG;SAC5B,CAAC;aACD,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAEpB,IAAI,WAAW,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,oCAAoC,MAAM,GAAG,EAC7C,WAAW,CACZ,CAAC;YACF,MAAM,IAAI,qCAA4B,CAAC,+BAA+B,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,gBAAgB,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,oBAAoB,MAAM,iBAAiB,SAAS,EAAE,CACvH,CAAC;QAEF,OAAO;YACL,OAAO,EAAE,IAAI;YACb,SAAS;YACT,YAAY,EAAE,GAAG;SAClB,CAAC;IACJ,CAAC;IAOD,KAAK,CAAC,YAAY,CAAC,MAAc;QAC/B,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CACpC,MAAM,EACN,kDAAkD,CACnD,CAAC;QAEF,OAAO;YACL,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,CAAC;YACxB,YAAY,EAAE,IAAI,CAAC,YAAY,IAAI,CAAC;YACpC,sBAAsB,EAAE,IAAI,CAAC,sBAAsB;SACpD,CAAC;IACJ,CAAC;IAQD,KAAK,CAAC,eAAe,CACnB,MAAc,EACd,SAAiB;QAEjB,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,4BAAmB,CAAC,yBAAyB,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,SAAS,aAAa,MAAM,EAAE,CAAC,CAAC;QAEtE,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAGlD,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,MAAM,QAAQ;aAC9D,IAAI,CAAC,iBAAiB,CAAC;aACvB,MAAM,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC;aAChC,EAAE,CAAC,IAAI,EAAE,SAAS,CAAC;aACnB,MAAM,CAAC,iBAAiB,CAAC;aACzB,MAAM,EAAE,CAAC;QAEZ,IAAI,YAAY,IAAI,CAAC,WAAW,EAAE,CAAC;YACjC,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,uCAAuC,SAAS,GAAG,EACnD,YAAY,CACb,CAAC;YACF,MAAM,IAAI,qCAA4B,CACpC,kCAAkC,CACnC,CAAC;QACJ,CAAC;QAED,MAAM,gBAAgB,GAAG,WAAyB,CAAC;QACnD,MAAM,EAAE,eAAe,EAAE,GAAG,gBAAgB,CAAC;QAG7C,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,gBAAgB,EAAE,GAAG,MAAM,QAAQ;aAClE,IAAI,CAAC,iBAAiB,CAAC;aACvB,MAAM,CAAC,QAAQ,CAAC;aAChB,EAAE,CAAC,iBAAiB,EAAE,eAAe,CAAC,CAAC;QAE1C,IAAI,gBAAgB,EAAE,CAAC;YACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,8CAA8C,eAAe,GAAG,EAChE,gBAAgB,CACjB,CAAC;YAEF,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC,EAAE,kBAAkB,EAAE,KAAK,EAAE,CAAC;QACxE,CAAC;QAED,MAAM,aAAa,GAAG,WAAuC,CAAC;QAC9D,MAAM,YAAY,GAChB,aAAa,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,KAAK,YAAY,CAAC,IAAI,KAAK,CAAC;QAEpE,IAAI,YAAY,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC,0BAA0B,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;QAClE,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC,EAAE,kBAAkB,EAAE,KAAK,EAAE,CAAC;IACxE,CAAC;IAEO,KAAK,CAAC,0BAA0B,CACtC,MAAc,EACd,WAAmB;QAEnB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8BAA8B,WAAW,gBAAgB,CAAC,CAAC;QAC3E,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAGlD,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,cAAc,EAAE,GAAG,MAAM,QAAQ;aAC9D,IAAI,CAAC,gBAAgB,CAAC;aACtB,MAAM,CAAC,mBAAmB,CAAC;aAC3B,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC;aACrB,MAAM,EAAE,CAAC;QAEZ,IAAI,cAAc,IAAI,CAAC,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,6BAA6B,WAAW,kBAAkB,EAC1D,cAAc,CACf,CAAC;YAEF,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC;QACvE,CAAC;QAGD,MAAM,EAAE,KAAK,EAAE,oBAAoB,EAAE,GAAG,MAAM,QAAQ;aACnD,IAAI,CAAC,gBAAgB,CAAC;aACtB,MAAM,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC;aAChC,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QAEzB,IAAI,oBAAoB,EAAE,CAAC;YACzB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,8BAA8B,WAAW,UAAU,EACnD,oBAAoB,CACrB,CAAC;QAEJ,CAAC;QAGD,MAAM,cAAc,GAAG,SAAyB,CAAC;QACjD,MAAM,aAAa,GAAG,cAAc,CAAC,iBAAiB,IAAI,CAAC,CAAC;QAC5D,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YACtB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,SAAS,CACvC,MAAM,EACN,aAAa,EACb,yBAAyB,WAAW,EAAE,CACvC,CAAC;YACF,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,aAAa,EAAE,YAAY,CAAC,WAAW;gBACvC,kBAAkB,EAAE,IAAI;aACzB,CAAC;QACJ,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC;IACvE,CAAC;CACF,CAAA;AApRY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;qCAImC,kCAAe;GAHlD,mBAAmB,CAoR/B"}