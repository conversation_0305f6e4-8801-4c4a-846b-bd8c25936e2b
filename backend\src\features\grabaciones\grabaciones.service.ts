import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { DatabaseService } from 'src/common/database.service';
import { StorageService } from 'src/shared/storage.service';
import { N8NWebhookService } from 'src/shared/n8n-webhook.service';
import { CreateGrabacionDto } from './dto/create-grabacion.dto';
import { Express } from 'express';
import { randomUUID } from 'crypto';
import { TableTypes } from 'src/common/database.types';

type Grabacion = TableTypes['grabaciones']['select'];

@Injectable()
export class GrabacionesService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly storageService: StorageService,
    private readonly n8nWebhookService: N8NWebhookService,
  ) {}

  async create(
    createGrabacionDto: CreateGrabacionDto,
    file: Express.Multer.File,
  ): Promise<Grabacion> {
    const fileExtension = file.originalname.split('.').pop();
    const filePath = `${createGrabacionDto.usuario_id}/${randomUUID()}.${fileExtension}`;

    // 1. Upload file to temporary storage
    const storagePath = await this.storageService.uploadToTemporal(
      file,
      'grabaciones',
      filePath,
    );

    const { data: publicUrlData } = this.databaseService
      .getClient()
      .storage.from('grabaciones')
      .getPublicUrl(storagePath);

    // 2. Create a record in the grabaciones table
    const { data: grabacion, error } = await this.databaseService
      .getClient()
      .from('grabaciones')
      .insert({
        ...createGrabacionDto,
        url_almacenamiento: publicUrlData.publicUrl,
        estado_procesamiento: 'pendiente',
      })
      .select()
      .single<Grabacion>();

    if (error || !grabacion) {
      throw new InternalServerErrorException(
        'Failed to create grabacion record.',
        error?.message,
      );
    }

    // 3. Trigger the asynchronous workflow
    await this.n8nWebhookService.triggerWorkflow('procesarGrabacion', {
      grabacionId: grabacion.id,
    });

    return grabacion;
  }
}
