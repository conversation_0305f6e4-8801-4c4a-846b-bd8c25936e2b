"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var GamificationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.GamificationService = void 0;
const common_1 = require("@nestjs/common");
const database_service_1 = require("../common/database.service");
let GamificationService = GamificationService_1 = class GamificationService {
    databaseService;
    logger = new common_1.Logger(GamificationService_1.name);
    constructor(databaseService) {
        this.databaseService = databaseService;
    }
    async _getUserOrFail(userId, columns = 'puntos, racha_actual, ultima_actividad_racha') {
        if (!userId || typeof userId !== 'string') {
            throw new common_1.BadRequestException('User ID must be a non-empty string.');
        }
        const supabase = this.databaseService.getClient();
        const { data, error } = await supabase
            .from('usuarios')
            .select(columns)
            .eq('id', userId)
            .single();
        if (error) {
            this.logger.error(`Failed to fetch user ${userId} from database:`, error);
            throw new common_1.InternalServerErrorException('Failed to fetch user data.');
        }
        if (!data) {
            throw new common_1.NotFoundException(`User with ID ${userId} not found.`);
        }
        return data;
    }
    async addPoints(userId, points, reason) {
        if (!points || points <= 0 || !Number.isInteger(points)) {
            throw new common_1.BadRequestException('Points must be a positive integer.');
        }
        this.logger.log(`Adding ${points} points to user ${userId}${reason ? ` (${reason})` : ''}`);
        const currentUser = await this._getUserOrFail(userId, 'puntos');
        const currentPoints = currentUser.puntos || 0;
        const newTotal = currentPoints + points;
        const supabase = this.databaseService.getClient();
        const { error: updateError } = await supabase
            .from('usuarios')
            .update({ puntos: newTotal })
            .eq('id', userId);
        if (updateError) {
            this.logger.error(`Failed to update points for user ${userId}:`, updateError);
            throw new common_1.InternalServerErrorException('Failed to update user points.');
        }
        this.logger.log(`Successfully added ${points} points to user ${userId}. New total: ${newTotal}`);
        return {
            success: true,
            newTotal,
            pointsAdded: points,
        };
    }
    async updateStreak(userId, action) {
        if (!action || !['increment', 'reset'].includes(action)) {
            throw new common_1.BadRequestException('Action must be either "increment" or "reset".');
        }
        this.logger.log(`${action === 'increment' ? 'Incrementing' : 'Resetting'} streak for user ${userId}`);
        const currentUser = await this._getUserOrFail(userId, 'racha_actual, ultima_actividad_racha');
        const currentStreak = currentUser.racha_actual || 0;
        const newStreak = action === 'increment' ? currentStreak + 1 : 0;
        const now = new Date().toISOString();
        const supabase = this.databaseService.getClient();
        const { error: updateError } = await supabase
            .from('usuarios')
            .update({
            racha_actual: newStreak,
            ultima_actividad_racha: now,
        })
            .eq('id', userId);
        if (updateError) {
            this.logger.error(`Failed to update streak for user ${userId}:`, updateError);
            throw new common_1.InternalServerErrorException('Failed to update user streak.');
        }
        this.logger.log(`Successfully ${action === 'increment' ? 'incremented' : 'reset'} streak for user ${userId}. New streak: ${newStreak}`);
        return {
            success: true,
            newStreak,
            lastActivity: now,
        };
    }
    async getUserStats(userId) {
        const user = await this._getUserOrFail(userId, 'id, puntos, racha_actual, ultima_actividad_racha');
        return {
            userId: user.id,
            puntos: user.puntos || 0,
            racha_actual: user.racha_actual || 0,
            ultima_actividad_racha: user.ultima_actividad_racha,
        };
    }
    async completeSubtask(userId, subtaskId) {
        if (!subtaskId) {
            throw new common_1.BadRequestException('Subtask ID is required.');
        }
        this.logger.log(`Completing subtask ${subtaskId} for user ${userId}`);
        const supabase = this.databaseService.getClient();
        const { data: subtaskData, error: subtaskError } = await supabase
            .from('retos_subtareas')
            .update({ estado: 'completada' })
            .eq('id', subtaskId)
            .select('reto_usuario_id')
            .single();
        if (subtaskError || !subtaskData) {
            this.logger.error(`Failed to complete or fetch subtask ${subtaskId}:`, subtaskError);
            throw new common_1.InternalServerErrorException('Failed to update subtask status.');
        }
        const typedSubtaskData = subtaskData;
        const { reto_usuario_id } = typedSubtaskData;
        const { data: allSubtasks, error: allSubtasksError } = await supabase
            .from('retos_subtareas')
            .select('estado')
            .eq('reto_usuario_id', reto_usuario_id);
        if (allSubtasksError) {
            this.logger.error(`Failed to fetch all subtasks for challenge ${reto_usuario_id}:`, allSubtasksError);
            return { success: true, pointsAwarded: 0, challengeCompleted: false };
        }
        const typedSubtasks = allSubtasks;
        const allCompleted = typedSubtasks?.every((st) => st.estado === 'completada') || false;
        if (allCompleted) {
            return this._handleChallengeCompletion(userId, reto_usuario_id);
        }
        return { success: true, pointsAwarded: 0, challengeCompleted: false };
    }
    async _handleChallengeCompletion(userId, challengeId) {
        this.logger.log(`All subtasks for challenge ${challengeId} are complete.`);
        const supabase = this.databaseService.getClient();
        const { data: challenge, error: challengeError } = await supabase
            .from('retos_usuarios')
            .select('puntos_recompensa')
            .eq('id', challengeId)
            .single();
        if (challengeError || !challenge) {
            this.logger.error(`Failed to fetch challenge ${challengeId} for completion:`, challengeError);
            return { success: true, pointsAwarded: 0, challengeCompleted: true };
        }
        const { error: challengeUpdateError } = await supabase
            .from('retos_usuarios')
            .update({ estado: 'completado' })
            .eq('id', challengeId);
        if (challengeUpdateError) {
            this.logger.error(`Failed to update challenge ${challengeId} status:`, challengeUpdateError);
        }
        const typedChallenge = challenge;
        const pointsToAward = typedChallenge.puntos_recompensa || 0;
        if (pointsToAward > 0) {
            const pointsResult = await this.addPoints(userId, pointsToAward, `Challenge completion: ${challengeId}`);
            return {
                success: true,
                pointsAwarded: pointsResult.pointsAdded,
                challengeCompleted: true,
            };
        }
        return { success: true, pointsAwarded: 0, challengeCompleted: true };
    }
};
exports.GamificationService = GamificationService;
exports.GamificationService = GamificationService = GamificationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [database_service_1.DatabaseService])
], GamificationService);
//# sourceMappingURL=gamification.service.js.map