import {
  Controller,
  Post,
  Body,
  UseInterceptors,
  UploadedFile,
  UseGuards,
  Request,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/common/auth/jwt-auth.guard';
import { GrabacionesService } from './grabaciones.service';
import { CreateGrabacionDto } from './dto/create-grabacion.dto';

interface AuthenticatedRequest extends Request {
  user: {
    sub: string;
  };
}

@ApiTags('Grabaciones')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('grabaciones')
export class GrabacionesController {
  constructor(private readonly grabacionesService: GrabacionesService) {}

  @Post()
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Upload a new recording' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Recording file and metadata',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        entidad_relacionada_id: {
          type: 'string',
          format: 'uuid',
        },
        entidad_relacionada_tipo: {
          type: 'string',
          enum: ['proceso_cliente', 'pregunta_cliente', 'tarea_cliente'],
        },
        tipo_grabacion: {
          type: 'string',
          enum: ['video_pantalla', 'solo_audio'],
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description:
      'The recording has been successfully uploaded and queued for processing.',
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  create(
    @Body() createGrabacionDto: CreateGrabacionDto,
    @UploadedFile() file: Express.Multer.File,
    @Request() req: AuthenticatedRequest,
  ) {
    const userId = req.user.sub;
    const dtoWithUser = { ...createGrabacionDto, usuario_id: userId };
    return this.grabacionesService.create(dtoWithUser, file);
  }
}
