# Troubleshooting Guide - Portal de Clientes

This directory contains detailed troubleshooting guides for common issues encountered during development and deployment of the Portal de Clientes application.

## Available Guides

### 1. [Shared Services & Security Issues](./1_Shared_Services_Security_Issues.md)
**Status:** ✅ Resolved  
**Module:** Backend - Authentication & Database  
**Issues Covered:**
- Supabase connection and authentication problems
- JWT token validation issues
- CORS configuration problems
- Environment variable setup

### 2. [Frontend Styling Issues](./2_Frontend_Styling_Issues.md)
**Status:** ✅ Resolved  
**Module:** Frontend - Styling & CSS Framework  
**Issues Covered:**
- Tailwind CSS v4 PostCSS configuration error
- Vite plugin vs PostCSS plugin conflicts
- CSS import syntax updates
- Component styling and responsive design

### 3. [Playwright Connection Error](./3_Playwright_Connection_Error.md)
**Status:** 🟡 Unresolved
**Module:** Testing - E2E
**Issues Covered:**
- `net::ERR_CONNECTION_REFUSED` when running Playwright tests
- Connectivity issues between Playwright browser and Vite dev server

### 4. [Shadcn UI CLI Issues](./4_Shadcn_UI_CLI_Issues.md)
**Status:** ✅ Resolved (Workaround)
**Module:** Frontend - UI Components
**Issues Covered:**
- `shadcn-ui` `init` command hangs on interactive prompts
- Inability to add new components via the CLI

## Quick Reference

### Most Common Issues

1. **Frontend won't start with Tailwind CSS errors**
   - See: [Frontend Styling Issues](./2_Frontend_Styling_Issues.md)
   - Quick fix: Check PostCSS configuration and CSS import syntax

2. **Authentication/Database connection issues**
   - See: [Shared Services & Security Issues](./1_Shared_Services_Security_Issues.md)
   - Quick fix: Verify environment variables and Supabase configuration

### How to Use These Guides

1. **Identify the Module:** Determine if the issue is frontend, backend, or infrastructure-related
2. **Check Status:** Look for guides marked as ✅ Resolved for similar issues
3. **Follow Solution Steps:** Each guide provides step-by-step resolution instructions
4. **Verify Fix:** Use the testing verification section to confirm the issue is resolved

### Contributing to Troubleshooting

When you encounter and resolve a new issue:

1. **Use the Template:** Copy `TEMPLATE.md` to create a new guide
2. **Document Thoroughly:** Include problem description, root cause, and solution
3. **Add Prevention Measures:** Help prevent the issue from recurring
4. **Update This README:** Add your new guide to the list above

### Emergency Contacts

For critical issues that cannot be resolved using these guides:
- **Development Team:** Check project documentation for current contacts
- **Infrastructure Issues:** Refer to deployment and hosting documentation

---

**Last Updated:** 2025-07-10  
**Maintained By:** Development Team
