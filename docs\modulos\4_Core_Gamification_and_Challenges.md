# Feature: 2. Core Gamification & Challenges

## 1. Overview

This module is the engine of the user experience, designed to motivate and guide employees through the process of knowledge extraction. It encompasses the systems for generating, presenting, and completing challenges, as well as the ranking and feedback mechanisms that drive engagement.

## 2. Requirements

*   **Requirement 1 (Daily Challenges):** The system must present a configurable number of daily challenges to each user on their main dashboard.
*   **Requirement 2 (Challenge Variety):** Challenges must support different types of actions, including defining processes, answering questions, and estimating durations.
*   **Requirement 3 (Points & Ranking):** Completing challenges must award users points, which are reflected in a real-time company ranking to foster friendly competition.
*   **Requirement 4 (Sprints):** All gamification activities must occur within a defined "Sprint" with a clear start and end date, creating a sense of purpose and urgency.
*   **Requirement 5 (Streaks):** The system must track a user's daily streak for completing at least one challenge, as a key engagement metric.
*   **Requirement 6 (Proactive Engagement):** Users who complete their daily challenges should have the option to request additional challenges.

## 3. UI/UX Design

*   **Key Screens/Components:**
    *   **Employee Dashboard (`/inicio`):** The main hub. Displays the Sprint progress bar, user's KPIs, daily challenge(s) in `<RetoCard>` components, and their current ranking.
    *   **All My Challenges Page (`/portal/todos-los-retos`):** A two-tab view showing all "Pending" and "Completed" challenges.
    *   **Ranking Page (`/portal/ranking`):** Displays the full company leaderboard, highlighting the current user and the top 3 with medals.
    *   **Challenge Interface (`/portal/retos/{id}`):** A dedicated page that presents the specific task for the selected challenge (e.g., the recording panel for "Define Process").
*   **User Flow (Completing a Challenge):**
    1.  User logs in and sees their daily challenge on the dashboard.
    2.  User clicks "Start Challenge".
    3.  They are navigated to the specific challenge interface.
    4.  User performs the required action (e.g., records a video and subsequently fills out the information sources form).
    5.  Upon submission, they receive a confirmation message.
    6.  For asynchronous tasks, they later receive a notification (`notificaciones` table) confirming completion and points awarded.
    7.  The user's points and streak are updated.

## 4. Technical Details

*   **Frontend:**
    *   **Relevant Components:** `<RetoCard>`, `<ProgressBar>`, `<RankingList>`, `<DataGrid>`.
    *   **State Management:** TanStack Query will manage fetching challenges, dashboard data, and rankings. A client-side store (Zustand) could manage UI state for the challenge interfaces.
    *   **API Interactions:**
        *   `GET /dashboard/home`: Fetches all data for the main dashboard.
        *   `GET /retos?estado=pendiente`: Fetches all pending challenges.
        *   `GET /retos?estado=completado`: Fetches all completed challenges.
        *   `GET /ranking`: Fetches the initial ranking data.
        *   Supabase Realtime subscription for live updates to the `usuarios` table (for points).
*   **Backend:**
    *   **Relevant API Routes:** `/dashboard`, `/retos`, `/ranking`.
    *   **Services Used:** `RetosService`, `GamificationService`, `DatabaseService`.
    *   **Database Tables Involved:** `retos_usuarios`, `retos_subtareas`, `usuarios`, `sprints_empresas`, `configuracion`.
    *   **External Services:** n8n (for the daily challenge generation workflow).
*   **n8n Workflow: "Generador Diario de Retos"**
    *   **Trigger:** Scheduled Cron Job (daily).
    *   **Inputs:** `empresa_id`, `usuario_id`.
    *   **Core Logic:**
        1.  Verify the user's company has an active sprint.
        2.  Identify pending tasks for the user (e.g., processes without detailed descriptions).
        3.  Call an LLM with the list of pending tasks to generate creative and engaging `titulo` and `descripcion` for the `retos_usuarios`.
        4.  Use the `configuracion` table to assign the correct `puntos_recompensa`.
        5.  Insert the new records into `retos_usuarios` and `retos_subtareas`.
    *   **Outputs:** New rows created in the database.

## 5. Acceptance Criteria

*   Given a user has pending challenges, when they visit their dashboard, then they should see the correct number of daily challenges.
*   Given a user completes a multi-step challenge like 'definir_proceso', when all steps (recording and information sources form) and asynchronous validation are complete, then their points and streak in the `usuarios` table should be updated correctly.
*   Given a user is on the ranking page, when another user gains points, then the ranking should update in near real-time.
*   Given a sprint ends, when a user visits the dashboard, then they should no longer be presented with new challenges.

## 6. Notes & Decisions

*   The logic for updating streaks (`racha_actual`) will be handled in a dedicated backend function within the `GamificationService`. This function will be called whenever a challenge is successfully completed, and it will compare the `ultima_actividad_racha` with the current date to determine if the streak should be incremented or reset.
*   The real-time ranking is deemed acceptable for the target company size (under 50 employees). The subscription will only be active when the user is on a page displaying the ranking, minimizing resource usage.