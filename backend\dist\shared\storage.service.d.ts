import { DatabaseService } from '../common/database.service';
interface UploadedFile {
    originalname: string;
    mimetype: string;
    size: number;
    buffer: Buffer;
}
export declare class StorageService {
    private readonly databaseService;
    private readonly logger;
    private readonly supabase;
    constructor(databaseService: DatabaseService);
    uploadToTemporal(file: UploadedFile, bucket: string, path: string): Promise<string>;
}
export {};
