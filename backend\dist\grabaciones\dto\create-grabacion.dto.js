"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateGrabacionDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class CreateGrabacionDto {
    entidad_relacionada_id;
    entidad_relacionada_tipo;
    usuario_id;
    tipo_grabacion;
}
exports.CreateGrabacionDto = CreateGrabacionDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the related entity',
        example: 'uuid-string',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateGrabacionDto.prototype, "entidad_relacionada_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of the related entity',
        enum: ['proceso_cliente', 'hallazgo_cliente', 'reto_subtarea'],
        example: 'reto_subtarea',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsIn)(['proceso_cliente', 'hallazgo_cliente', 'reto_subtarea']),
    __metadata("design:type", String)
], CreateGrabacionDto.prototype, "entidad_relacionada_tipo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the user uploading the file',
        example: 'uuid-string',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateGrabacionDto.prototype, "usuario_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of recording',
        enum: ['video', 'audio', 'pantalla'],
        example: 'video',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsIn)(['video', 'audio', 'pantalla']),
    __metadata("design:type", String)
], CreateGrabacionDto.prototype, "tipo_grabacion", void 0);
//# sourceMappingURL=create-grabacion.dto.js.map