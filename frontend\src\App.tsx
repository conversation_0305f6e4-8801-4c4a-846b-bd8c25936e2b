import { Routes, Route } from 'react-router-dom';
import DemoPage from './pages/demo';
import LoginPage from './pages/LoginPage';
import { ForgotPasswordPage } from './pages/ForgotPasswordPage';
import { ResetPasswordPage } from './pages/ResetPasswordPage';
import GlobalToast from './components/ui/GlobalToast';
import { useNotifications } from './hooks/useNotifications';
import PersistLogin from './features/auth/components/PersistLogin';
import RequireAuth from './features/auth/components/RequireAuth';
import AuthLayout from './features/auth/components/AuthLayout';

const ROLES = {
  CLIENTE_EMPLEADO: 'cliente_empleado',
  CLIENTE_GERENTE: 'cliente_gerente',
};

function App() {
  useNotifications();

  return (
    <>
      <GlobalToast />
      <Routes>
        <Route path="/login" element={<AuthLayout title="Inicia sesión en tu cuenta" description="Accede a tu panel de control personalizado y gestiona tu cuenta de forma segura"><LoginPage /></AuthLayout>} />
        <Route path="/forgot-password" element={<AuthLayout title="Recuperar Contraseña" description="Introduce tu email para recibir un enlace de recuperación"><ForgotPasswordPage /></AuthLayout>} />
        <Route path="/update-password" element={<AuthLayout title="Actualizar Contraseña" description="Introduce tu nueva contraseña"><ResetPasswordPage /></AuthLayout>} />

        {/* Routes that require login persistence */}
        <Route element={<PersistLogin />}>
          {/* Protected Routes */}
          <Route
            element={<RequireAuth allowedRoles={[ROLES.CLIENTE_EMPLEADO, ROLES.CLIENTE_GERENTE]} />}
          >
            <Route path="/" element={<div>Home</div>} />
            <Route path="/inicio" element={<div>Dashboard</div>} />
            <Route path="/demo" element={<DemoPage />} />
          </Route>
        </Route>
        
        <Route path="/unauthorized" element={<div>Unauthorized</div>} />

      </Routes>
    </>
  );
}

export default App;
