# Sprint 18 (Revised): Asynchronous Error State Handling

## 1. Sprint Goal
The goal of this sprint is to make the application resilient and informative when an asynchronous workflow fails. This involves implementing the application-side logic to correctly interpret and display error states that are set in the database by the external n8n error-handling workflow.

## 2. Key Tasks

### 2.a. Define the "Error" Contract
- The application code must be aware of the specific database changes that signify a processing error. This is the "contract" for failure cases.
- **Expected Changes on Error:**
    - **In `grabaciones` table:** `estado_procesamiento` is set to `error`.
    - **In `grabaciones` table:** `info_adicional` field contains a user-friendly (or technical) error message.
    - **(Optional) In `notificaciones` table:** A new record is created to inform the user of a non-critical, recoverable error.

### 2.b. Frontend UI for Error States
- **Update Data Display Components:** Modify frontend components to visually indicate when a submitted item has an error. For example, a "Define Process" challenge might show a red "Error" badge next to a failed recording.
- **Display Error Details:** When a user hovers over or clicks on the error indicator, a tooltip or modal should display the `info_adicional` message from the `grabaciones` table, providing context for the failure.

### 2.c. Development Coordination
- **Action Item:** The developer must coordinate with the n8n workflow manager to agree on the exact values and messages that will be written to the database in case of an error, ensuring the frontend can handle them gracefully.

## 3. Acceptance Criteria
- When a `grabaciones` record's `estado_procesamiento` is manually set to `error` in the database, the frontend UI updates to show a clear error indicator for that item.
- Hovering over the error indicator displays the error message stored in the `info_adicional` field.
- The application does not crash or behave unexpectedly when encountering records in an error state.

## 4. Key Files to Be Created/Modified
- `frontend/src/features/data-capture/components/DefinirProcesoPanel.tsx` (Modified to handle error states in the recordings list)
- `frontend/src/features/data-capture/components/ChallengeStatusBadge.tsx` (New or modified component to show "Pending", "Completed", "Error")
- `docs/ARCHITECTURE.md` (Modified to document the error state contract)