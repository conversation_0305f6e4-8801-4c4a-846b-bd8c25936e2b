import { DatabaseService } from 'src/common/database.service';
import { N8NWebhookService } from 'src/shared/n8n-webhook.service';
import { StorageService } from 'src/shared/storage.service';
import { CreateGrabacionDto } from './dto/create-grabacion.dto';
interface UploadedFile {
    originalname: string;
    mimetype: string;
    size: number;
    buffer: Buffer;
}
export interface Grabacion {
    id: string;
    entidad_relacionada_id: string;
    entidad_relacionada_tipo: string;
    usuario_id: string;
    tipo_grabacion: string;
    url_almacenamiento: string;
    estado_procesamiento: string;
}
export declare class GrabacionesService {
    private readonly databaseService;
    private readonly storageService;
    private readonly n8nWebhookService;
    constructor(databaseService: DatabaseService, storageService: StorageService, n8nWebhookService: N8NWebhookService);
    create(file: UploadedFile, body: CreateGrabacionDto): Promise<Grabacion>;
}
export {};
