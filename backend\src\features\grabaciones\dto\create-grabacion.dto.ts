import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsUUID } from 'class-validator';

enum EntidadRelacionadaTipo {
  PROCESO_CLIENTE = 'proceso_cliente',
  PREGUNTA_CLIENTE = 'pregunta_cliente',
  TAREA_CLIENTE = 'tarea_cliente',
}

enum TipoGrabacion {
  VIDEO_PANTALLA = 'video_pantalla',
  SOLO_AUDIO = 'solo_audio',
}

export class CreateGrabacionDto {
  @ApiProperty({
    description: 'The ID of the entity this recording is related to.',
    example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
  })
  @IsNotEmpty()
  @IsUUID()
  entidad_relacionada_id: string;

  @ApiProperty({
    description: 'The type of the related entity.',
    enum: EntidadRelacionadaTipo,
    example: EntidadRelacionadaTipo.PROCESO_CLIENTE,
  })
  @IsNotEmpty()
  @IsEnum(EntidadRelacionadaTipo)
  entidad_relacionada_tipo: EntidadRelacionadaTipo;

  @ApiProperty({
    description: 'The type of the recording.',
    enum: TipoGrabacion,
    example: TipoGrabacion.VIDEO_PANTALLA,
  })
  @IsNotEmpty()
  @IsEnum(TipoGrabacion)
  tipo_grabacion: TipoGrabacion;

  @ApiProperty({
    description: 'The ID of the user who uploaded the recording.',
  })
  @IsNotEmpty()
  @IsUUID()
  usuario_id: string;
}