"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateDuracionDto = exports.FrecuenciaPeriodo = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
var FrecuenciaPeriodo;
(function (FrecuenciaPeriodo) {
    FrecuenciaPeriodo["diario"] = "diario";
    FrecuenciaPeriodo["semanal"] = "semanal";
    FrecuenciaPeriodo["mensual"] = "mensual";
    FrecuenciaPeriodo["trimestral"] = "trimestral";
    FrecuenciaPeriodo["anual"] = "anual";
    FrecuenciaPeriodo["indefinido"] = "indefinido";
})(FrecuenciaPeriodo || (exports.FrecuenciaPeriodo = FrecuenciaPeriodo = {}));
class UpdateDuracionDto {
    duracion_minutos_por_ejecucion;
    frecuencia_periodo;
    frecuencia_ocurrencias;
}
exports.UpdateDuracionDto = UpdateDuracionDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Duración estimada en minutos de una única ejecución del proceso.',
        example: 60,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], UpdateDuracionDto.prototype, "duracion_minutos_por_ejecucion", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Periodo de tiempo en el que se repite el proceso.',
        enum: FrecuenciaPeriodo,
        example: FrecuenciaPeriodo.semanal,
    }),
    (0, class_validator_1.IsEnum)(FrecuenciaPeriodo),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], UpdateDuracionDto.prototype, "frecuencia_periodo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Número de veces que ocurre el proceso en el periodo definido.',
        example: 5,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], UpdateDuracionDto.prototype, "frecuencia_ocurrencias", void 0);
//# sourceMappingURL=update-duracion.dto.js.map