import { OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SupabaseClient } from '@supabase/supabase-js';
import { Db } from './database.types';
export declare class DatabaseService implements OnModuleInit {
    private readonly configService;
    private supabase;
    constructor(configService: ConfigService);
    onModuleInit(): void;
    getClient(): SupabaseClient<Db>;
}
