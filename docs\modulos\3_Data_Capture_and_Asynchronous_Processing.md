# Feature: 3. Data Capture & Asynchronous Processing

## 1. Overview

This module is the core data ingestion and processing pipeline of the application. It defines how users submit unstructured data (screen recordings, audio answers) and structured data (form-based answers), and how this data is securely stored, processed by asynchronous workflows, and ultimately transformed into valuable, structured information.

## 2. Requirements

*   **Requirement 1 (Multi-modal Input):** The system must accept user input via screen and voice recording, audio-only recording, and structured web forms.
*   **Requirement 2 (Asynchronous Processing):** All media-based submissions must be processed asynchronously to provide a fast UI response to the user. The backend should not be blocked waiting for AI analysis.
*   **Requirement 3 (Robust Storage Flow):** A two-stage storage system must be used for media files: a temporary "inbox" (Supabase Storage) for fast uploads, and permanent, scalable storage (Google Cloud Storage) for long-term retention.
*   **Requirement 4 (Reliable Orchestration):** The system must use n8n as the orchestrator to trigger and manage the complex, multi-step AI analysis and validation workflows.
*   **Requirement 5 (Data Integrity):** The system must maintain clear links between the original submission (e.g., a `grabaciones` record) and the resulting structured data (e.g., an updated `procesos_clientes` record).
*   **Requirement 6 (Information Sources Documentation):** After recording, users must document required information sources for the process, including name, description, owner (from `personas`), format, and optional attachment, stored in `procesos_fuentes_informacion`.

## 3. UI/UX Design

*   **Key Screens/Components:**
    *   **Define Process Panel (`<DefinirProcesoPanel />`):** A dedicated UI for the `definir_proceso` action. It includes instructions, a list of previous recordings for that process, the `<FileUpload />` component for screen and voice recording, and a new `<InformationSourcesForm />` for documenting required information sources.
    *   **Answer Question Panel (`<ResponderPreguntaPanel />`):** A UI for the `responder_pregunta` action, offering tabs for "Written Answer" (a textarea) and "Record Audio" (the `<FileUpload />` component for audio).
    *   **Define Duration Form (`<DefinirDuracionForm />`):** A simple form for the `definir_duracion_proceso` action with numeric inputs and a dropdown.
    *   **Information Sources Form (`<InformationSourcesForm />`):** A reusable component for adding/editing rows of information sources, with fields for name, description, person selector, format text, and optional file upload.
*   **User Flow (Asynchronous Submission):**
    1.  User interacts with a challenge interface (e.g., Define Process Panel).
    2.  They record and submit a media file (Step 1).
    3.  The UI shows a loading indicator and immediately displays a confirmation message upon successful upload to the temporary storage ("¡Grabación enviada! La procesaremos...").
    4.  The UI transitions to Step 2: the Information Sources Form, where the user adds rows of required information.
    5.  Upon submitting the form, the data is saved synchronously, and the process is marked as complete if both steps are done.
    6.  The user can continue using the application without waiting.
    7.  Later, a notification appears, informing them of the outcome (e.g., "¡Has ganado 100 puntos!").

## 4. Technical Details

*   **Frontend:**
    *   **Relevant Components:** `<FileUpload>`, `<DefinirProcesoPanel />`, `<ResponderPreguntaPanel />`, `<InformationSourcesForm />`.
    *   **State Management:** TanStack Query will be used to handle the mutation (POST request) for the submission. The component's local state will manage the recording/uploading status and multi-step flow.
    *   **API Interactions:**
        *   `POST /grabaciones`: For submitting video or audio files.
        *   `POST /respuestas-pregunta`: For submitting written answers.
        *   `PATCH /procesos-clientes/{id}/duracion`: For submitting the duration form.
        *   `POST /procesos-fuentes-informacion`: For submitting information sources data.
*   **Backend:**
    *   **Relevant API Routes:** As listed above, plus new routes for information sources CRUD.
    *   **Services Used:** `StorageService`, `GrabacionesService`, `N8N_WebhookService`, `DatabaseService`, new `InformationSourcesService`.
    *   **Database Tables Involved:** `grabaciones`, `procesos_clientes`, `preguntas`, `retos_subtareas`, `procesos_fuentes_informacion`.
    *   **External Services:** Supabase Storage (temporary), Google Cloud Storage (permanent), n8n.
*   **n8n Workflow: "Procesador y Validador de Grabaciones"**
    *   **Trigger:** Webhook called by the backend.
    *   **Inputs:** `{ "grabacionId": "uuid-of-the-recording" }`.
    *   **Core Logic:**
        1.  **Fetch Record:** Get the `grabaciones` record from the database using the `grabacionId`.
        2.  **Download from Temp:** Download the media file from the temporary `url_almacenamiento` (Supabase Storage).
        3.  **Upload to Permanent:** Upload the file to the designated bucket in Google Cloud Storage.
        4.  **Update URL:** Update the `url_almacenamiento` in the `grabaciones` table to the new GCS URL.
        5.  **AI Analysis:** Send the file to the appropriate AI service (e.g., Gemini) for transcription and analysis.
        6.  **Validate & Update:** Based on the AI output, update the relevant business table (e.g., `procesos_clientes.descripcion_detallada`).
        7.  **Check Information Sources:** Verify if information sources have been submitted for the process.
        8.  **Mark as Complete:** Update the corresponding `retos_subtareas` record to `completado` only if both recording and information sources are complete.
        9.  **Award Points:** Call the `GamificationService` to add points and update the user's streak.
        10. **Send Notification:** Create a new record in the `notificaciones` table.
        11. **Cleanup:** Delete the file from temporary Supabase Storage.
    *   **Outputs:** Updated database records, new notification record.

## 5. Acceptance Criteria

*   Given a user submits a video file, when the upload is complete, then a new record must exist in the `grabaciones` table with `estado_procesamiento` as `pendiente`.
*   Given a `grabaciones` record is created, when the n8n webhook is triggered, then the file must be moved from temporary to permanent storage and its URL updated in the database.
*   Given an n8n workflow completes successfully, when the AI analysis is positive, then the corresponding `retos_subtareas` status must be `completado` and the user must have received points.
*   Given a user submits a simple form (like duration), when the submission is successful, then the relevant database table must be updated directly and the user must receive points immediately (synchronously).
*   Given a user completes the information sources form, when submitted, then new records are created in `procesos_fuentes_informacion` and the process is checked for completion.

## 6. Notes & Decisions

*   The separation of temporary and permanent storage is a critical architectural decision for ensuring both a responsive UI and a scalable, cost-effective storage solution.
*   The `N8N_WebhookService` in the backend will be a simple, reusable service that centralizes the logic for triggering all n8n workflows, making the system easier to maintain. It will fetch the specific webhook URL from a configuration file or environment variable.