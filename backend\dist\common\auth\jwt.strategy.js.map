{"version": 3, "file": "jwt.strategy.js", "sourceRoot": "", "sources": ["../../../src/common/auth/jwt.strategy.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAmE;AACnE,2CAA+C;AAC/C,+CAAoD;AACpD,+CAAoD;AACpD,0DAAsD;AAW/C,IAAM,WAAW,GAAjB,MAAM,WAAY,SAAQ,IAAA,2BAAgB,EAAC,uBAAQ,CAAC;IAEtC;IACA;IAFnB,YACmB,aAA4B,EAC5B,eAAgC;QAEjD,MAAM,MAAM,GAAG,aAAa,CAAC,GAAG,CAAS,qBAAqB,CAAC,CAAC;QAChE,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QACD,KAAK,CAAC;YACJ,cAAc,EAAE,yBAAU,CAAC,2BAA2B,EAAE;YACxD,gBAAgB,EAAE,KAAK;YACvB,WAAW,EAAE,MAAM;SACpB,CAAC,CAAC;QAXc,kBAAa,GAAb,aAAa,CAAe;QAC5B,oBAAe,GAAf,eAAe,CAAiB;IAWnD,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAAmB;QAChC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe;aACxC,SAAS,EAAE;aACX,IAAI,CAAC,UAAU,CAAC;aAChB,MAAM,CAAC,GAAG,CAAC;aACX,EAAE,CAAC,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC;aAC/B,MAAM,EAAE,CAAC;QAEZ,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAgB,CAAC;QACvC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;QAE7B,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;YACnB,MAAM,IAAI,8BAAqB,EAAE,CAAC;QACpC,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF,CAAA;AAjCY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;qCAGuB,sBAAa;QACX,kCAAe;GAHxC,WAAW,CAiCvB"}